# -*- coding: utf-8 -*-
"""
综合音频设备测试工具
尝试多种方法确保音频输出到指定设备
"""

import sys
import os
import numpy as np
import tempfile
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QComboBox, QPushButton, QLabel, QTextEdit, QGroupBox, QTabWidget,
    QCheckBox, QSpinBox, QSlider
)
from PyQt5.QtCore import Qt, QIODevice, QByteArray, QBuffer, QUrl, QTimer
from PyQt5.QtMultimedia import (
    QAudioDeviceInfo, QAudio, QAudioOutput, QAudioFormat,
    QMediaPlayer, QMediaContent
)

try:
    import soundfile as sf
    AUDIO_PROCESSING_AVAILABLE = True
except ImportError:
    AUDIO_PROCESSING_AVAILABLE = False

class ComprehensiveAudioTest(QMainWindow):
    """综合音频设备测试"""
    
    def __init__(self):
        super().__init__()
        self.selected_device = None
        self.audio_output = None
        self.audio_buffer = None
        self.media_player = QMediaPlayer()
        self.temp_files = []
        self.setup_ui()
        self.populate_devices()
        
    def setup_ui(self):
        """设置界面"""
        self.setWindowTitle("🔊 综合音频设备测试工具")
        self.setGeometry(100, 100, 700, 600)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 设备选择组
        device_group = QGroupBox("🎯 设备选择")
        device_layout = QVBoxLayout(device_group)
        
        device_layout.addWidget(QLabel("选择音频输出设备:"))
        self.device_combo = QComboBox()
        self.device_combo.currentTextChanged.connect(self.on_device_changed)
        device_layout.addWidget(self.device_combo)
        
        refresh_btn = QPushButton("🔄 刷新设备列表")
        refresh_btn.clicked.connect(self.populate_devices)
        device_layout.addWidget(refresh_btn)
        
        layout.addWidget(device_group)
        
        # 测试选项卡
        tab_widget = QTabWidget()
        
        # 方法1：直接QAudioOutput
        tab1 = QWidget()
        tab1_layout = QVBoxLayout(tab1)
        
        tab1_layout.addWidget(QLabel("🔧 方法1: 直接使用QAudioOutput"))
        
        method1_btn = QPushButton("🎵 测试方法1 (QBuffer)")
        method1_btn.clicked.connect(self.test_method1_qbuffer)
        tab1_layout.addWidget(method1_btn)
        
        method1b_btn = QPushButton("🎵 测试方法1B (推送模式)")
        method1b_btn.clicked.connect(self.test_method1_push)
        tab1_layout.addWidget(method1b_btn)
        
        tab1_layout.addStretch()
        tab_widget.addTab(tab1, "方法1")
        
        # 方法2：临时文件 + QMediaPlayer
        tab2 = QWidget()
        tab2_layout = QVBoxLayout(tab2)
        
        tab2_layout.addWidget(QLabel("🔧 方法2: 临时文件 + QMediaPlayer"))
        tab2_layout.addWidget(QLabel("注意：QMediaPlayer可能无法指定输出设备"))
        
        method2_btn = QPushButton("🎵 测试方法2")
        method2_btn.clicked.connect(self.test_method2_file)
        tab2_layout.addWidget(method2_btn)
        
        tab2_layout.addStretch()
        tab_widget.addTab(tab2, "方法2")
        
        # 方法3：系统级测试
        tab3 = QWidget()
        tab3_layout = QVBoxLayout(tab3)
        
        tab3_layout.addWidget(QLabel("🔧 方法3: 系统级音频测试"))
        
        # 频率控制
        freq_layout = QHBoxLayout()
        freq_layout.addWidget(QLabel("测试频率:"))
        self.freq_spin = QSpinBox()
        self.freq_spin.setRange(100, 2000)
        self.freq_spin.setValue(440)
        freq_layout.addWidget(self.freq_spin)
        freq_layout.addWidget(QLabel("Hz"))
        tab3_layout.addLayout(freq_layout)
        
        # 持续时间控制
        duration_layout = QHBoxLayout()
        duration_layout.addWidget(QLabel("持续时间:"))
        self.duration_spin = QSpinBox()
        self.duration_spin.setRange(1, 10)
        self.duration_spin.setValue(3)
        duration_layout.addWidget(self.duration_spin)
        duration_layout.addWidget(QLabel("秒"))
        tab3_layout.addLayout(duration_layout)
        
        method3_btn = QPushButton("🎵 播放长音测试")
        method3_btn.clicked.connect(self.test_method3_long_tone)
        tab3_layout.addWidget(method3_btn)
        
        tab3_layout.addStretch()
        tab_widget.addTab(tab3, "方法3")
        
        layout.addWidget(tab_widget)
        
        # 控制按钮
        control_layout = QHBoxLayout()
        
        self.stop_btn = QPushButton("⏹️ 停止所有播放")
        self.stop_btn.clicked.connect(self.stop_all_audio)
        control_layout.addWidget(self.stop_btn)
        
        self.clear_btn = QPushButton("🗑️ 清理临时文件")
        self.clear_btn.clicked.connect(self.cleanup_temp_files)
        control_layout.addWidget(self.clear_btn)
        
        layout.addLayout(control_layout)
        
        # 状态显示
        layout.addWidget(QLabel("📊 测试结果:"))
        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(150)
        self.status_text.setReadOnly(True)
        layout.addWidget(self.status_text)
        
    def populate_devices(self):
        """填充设备列表"""
        self.device_combo.clear()
        self.log("🔍 检测音频输出设备...")
        
        try:
            device_list = QAudioDeviceInfo.availableDevices(QAudio.AudioOutput)
            
            if not device_list:
                self.log("❌ 未找到音频输出设备")
                return
            
            for i, device in enumerate(device_list):
                if not device.isNull():
                    device_name = device.deviceName()
                    self.device_combo.addItem(f"{i+1}. {device_name}", device)
                    self.log(f"✅ 设备 {i+1}: {device_name}")
            
            if self.device_combo.count() > 0:
                self.device_combo.setCurrentIndex(0)
                
        except Exception as e:
            self.log(f"❌ 检测设备失败: {e}")
            
    def on_device_changed(self, device_name):
        """设备选择改变"""
        self.selected_device = self.device_combo.currentData()
        if self.selected_device:
            self.log(f"🎯 选择设备: {self.selected_device.deviceName()}")
            self.show_device_capabilities()
        else:
            self.log("⚠️ 未选择有效设备")
            
    def show_device_capabilities(self):
        """显示设备能力"""
        if not self.selected_device:
            return
            
        try:
            preferred = self.selected_device.preferredFormat()
            self.log(f"📋 设备能力:")
            self.log(f"  - 首选采样率: {preferred.sampleRate()} Hz")
            self.log(f"  - 首选声道数: {preferred.channelCount()}")
            self.log(f"  - 首选采样大小: {preferred.sampleSize()} bits")
            
        except Exception as e:
            self.log(f"❌ 获取设备信息失败: {e}")
            
    def generate_test_audio(self, duration=None, frequency=None):
        """生成测试音频"""
        if duration is None:
            duration = self.duration_spin.value()
        if frequency is None:
            frequency = self.freq_spin.value()
            
        sample_rate = 44100
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        audio_data = 0.3 * np.sin(2 * np.pi * frequency * t)
        
        # 转换为立体声
        stereo_audio = np.column_stack((audio_data, audio_data))
        return stereo_audio, sample_rate
        
    def test_method1_qbuffer(self):
        """方法1: QBuffer方式"""
        if not self.selected_device:
            self.log("❌ 请先选择设备")
            return
            
        try:
            self.log("🧪 测试方法1: QBuffer方式")
            
            # 生成音频
            audio_data, sample_rate = self.generate_test_audio()
            
            # 转换格式
            audio_data_int16 = (audio_data * 32767).astype(np.int16)
            audio_bytes = audio_data_int16.tobytes()
            
            # 创建音频格式
            format = QAudioFormat()
            format.setSampleRate(sample_rate)
            format.setChannelCount(2)
            format.setSampleSize(16)
            format.setCodec("audio/pcm")
            format.setByteOrder(QAudioFormat.LittleEndian)
            format.setSampleType(QAudioFormat.SignedInt)
            
            # 检查格式支持
            if not self.selected_device.isFormatSupported(format):
                format = self.selected_device.nearestFormat(format)
                self.log("⚠️ 使用设备首选格式")
            
            # 创建音频输出
            self.audio_output = QAudioOutput(self.selected_device, format)
            
            # 创建缓冲区
            self.audio_buffer = QBuffer()
            self.audio_buffer.setData(QByteArray(audio_bytes))
            self.audio_buffer.open(QIODevice.ReadOnly)
            
            # 开始播放
            self.audio_output.start(self.audio_buffer)
            
            state = self.audio_output.state()
            self.log(f"✅ 方法1播放状态: {state}")
            
            if state == QAudio.ActiveState:
                self.log("🎧 请检查指定设备是否有声音！")
            
        except Exception as e:
            self.log(f"❌ 方法1失败: {e}")
            
    def test_method1_push(self):
        """方法1B: 推送模式"""
        if not self.selected_device:
            self.log("❌ 请先选择设备")
            return
            
        try:
            self.log("🧪 测试方法1B: 推送模式")
            
            # 生成音频
            audio_data, sample_rate = self.generate_test_audio()
            
            # 转换格式
            audio_data_int16 = (audio_data * 32767).astype(np.int16)
            audio_bytes = audio_data_int16.tobytes()
            
            # 创建音频格式
            format = QAudioFormat()
            format.setSampleRate(sample_rate)
            format.setChannelCount(2)
            format.setSampleSize(16)
            format.setCodec("audio/pcm")
            format.setByteOrder(QAudioFormat.LittleEndian)
            format.setSampleType(QAudioFormat.SignedInt)
            
            if not self.selected_device.isFormatSupported(format):
                format = self.selected_device.nearestFormat(format)
            
            # 创建音频输出
            self.audio_output = QAudioOutput(self.selected_device, format)
            
            # 推送模式
            io_device = self.audio_output.start()
            if io_device:
                bytes_written = io_device.write(audio_bytes)
                self.log(f"✅ 方法1B写入: {bytes_written} 字节")
                self.log("🎧 请检查指定设备是否有声音！")
            else:
                self.log("❌ 方法1B无法启动")
                
        except Exception as e:
            self.log(f"❌ 方法1B失败: {e}")
            
    def test_method2_file(self):
        """方法2: 临时文件方式"""
        try:
            self.log("🧪 测试方法2: 临时文件 + QMediaPlayer")
            
            # 生成音频
            audio_data, sample_rate = self.generate_test_audio()
            
            # 保存到临时文件
            temp_file = tempfile.NamedTemporaryFile(suffix='.wav', delete=False)
            temp_file_path = temp_file.name
            temp_file.close()
            
            if AUDIO_PROCESSING_AVAILABLE:
                sf.write(temp_file_path, audio_data, sample_rate)
            else:
                self.log("❌ 需要soundfile库来保存音频文件")
                return
            
            self.temp_files.append(temp_file_path)
            
            # 使用QMediaPlayer播放
            media_content = QMediaContent(QUrl.fromLocalFile(temp_file_path))
            self.media_player.setMedia(media_content)
            self.media_player.setVolume(80)
            self.media_player.play()
            
            self.log("✅ 方法2播放开始")
            self.log("⚠️ 注意：QMediaPlayer可能使用系统默认设备")
            
        except Exception as e:
            self.log(f"❌ 方法2失败: {e}")
            
    def test_method3_long_tone(self):
        """方法3: 长音测试"""
        if not self.selected_device:
            self.log("❌ 请先选择设备")
            return
            
        try:
            self.log("🧪 测试方法3: 长音测试")
            
            duration = self.duration_spin.value()
            frequency = self.freq_spin.value()
            
            self.log(f"🎵 播放 {frequency}Hz 音调，持续 {duration} 秒")
            
            # 使用方法1B进行长音测试
            self.test_method1_push()
            
        except Exception as e:
            self.log(f"❌ 方法3失败: {e}")
            
    def stop_all_audio(self):
        """停止所有音频播放"""
        try:
            if self.audio_output:
                self.audio_output.stop()
                self.log("⏹️ QAudioOutput已停止")
                
            if self.audio_buffer:
                self.audio_buffer.close()
                self.audio_buffer = None
                
            self.media_player.stop()
            self.log("⏹️ QMediaPlayer已停止")
            
        except Exception as e:
            self.log(f"❌ 停止播放失败: {e}")
            
    def cleanup_temp_files(self):
        """清理临时文件"""
        for temp_file in self.temp_files:
            try:
                if os.path.exists(temp_file):
                    os.unlink(temp_file)
            except Exception as e:
                self.log(f"❌ 清理文件失败: {e}")
        
        self.temp_files.clear()
        self.log("🗑️ 临时文件已清理")
        
    def log(self, message):
        """记录日志"""
        self.status_text.append(message)
        cursor = self.status_text.textCursor()
        cursor.movePosition(cursor.End)
        self.status_text.setTextCursor(cursor)
        print(message)
        
    def closeEvent(self, event):
        """关闭事件"""
        self.stop_all_audio()
        self.cleanup_temp_files()
        event.accept()

def main():
    app = QApplication(sys.argv)
    window = ComprehensiveAudioTest()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
