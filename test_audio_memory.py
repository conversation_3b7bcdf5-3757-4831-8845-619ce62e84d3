# -*- coding: utf-8 -*-
"""
测试音频播放器参数记忆功能
验证参数是否能正确保存和加载
"""

import sys
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QDoubleSpinBox, QSpinBox, QComboBox, QTextEdit,
    QGroupBox, QFormLayout
)
from PyQt5.QtCore import Qt, QSettings
from PyQt5.QtGui import QFont

class AudioMemoryTest(QMainWindow):
    """音频参数记忆功能测试"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.load_settings()
        self.connect_signals()
        
    def setup_ui(self):
        """设置界面"""
        self.setWindowTitle("🧠 音频播放器参数记忆功能测试")
        self.setGeometry(100, 100, 600, 700)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title = QLabel("🧠 音频播放器参数记忆功能测试")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 说明
        info = QLabel("修改下面的参数，关闭程序后重新打开，检查参数是否被记住")
        info.setAlignment(Qt.AlignCenter)
        info.setStyleSheet("color: #666; margin: 10px;")
        layout.addWidget(info)
        
        # 音频参数组
        audio_group = QGroupBox("🎵 音频播放参数")
        audio_layout = QFormLayout(audio_group)
        
        # 音量范围
        volume_layout = QHBoxLayout()
        self.volume_min_spin = QDoubleSpinBox()
        self.volume_min_spin.setRange(0.0, 1.0)
        self.volume_min_spin.setSingleStep(0.1)
        self.volume_min_spin.setValue(0.2)
        self.volume_min_spin.setSuffix(" (最小)")
        
        self.volume_max_spin = QDoubleSpinBox()
        self.volume_max_spin.setRange(0.0, 1.0)
        self.volume_max_spin.setSingleStep(0.1)
        self.volume_max_spin.setValue(0.8)
        self.volume_max_spin.setSuffix(" (最大)")
        
        volume_layout.addWidget(self.volume_min_spin)
        volume_layout.addWidget(self.volume_max_spin)
        audio_layout.addRow("🔊 音量范围:", volume_layout)
        
        # 增益范围
        gain_layout = QHBoxLayout()
        self.gain_min_spin = QDoubleSpinBox()
        self.gain_min_spin.setRange(-10.0, 10.0)
        self.gain_min_spin.setSingleStep(0.5)
        self.gain_min_spin.setValue(-2.0)
        self.gain_min_spin.setSuffix(" dB (最小)")
        
        self.gain_max_spin = QDoubleSpinBox()
        self.gain_max_spin.setRange(-10.0, 10.0)
        self.gain_max_spin.setSingleStep(0.5)
        self.gain_max_spin.setValue(2.0)
        self.gain_max_spin.setSuffix(" dB (最大)")
        
        gain_layout.addWidget(self.gain_min_spin)
        gain_layout.addWidget(self.gain_max_spin)
        audio_layout.addRow("🎚️ 增益范围:", gain_layout)
        
        # 音调范围
        pitch_layout = QHBoxLayout()
        self.pitch_min_spin = QDoubleSpinBox()
        self.pitch_min_spin.setRange(0.5, 2.0)
        self.pitch_min_spin.setSingleStep(0.1)
        self.pitch_min_spin.setValue(0.9)
        self.pitch_min_spin.setSuffix("x (最小)")
        
        self.pitch_max_spin = QDoubleSpinBox()
        self.pitch_max_spin.setRange(0.5, 2.0)
        self.pitch_max_spin.setSingleStep(0.1)
        self.pitch_max_spin.setValue(1.1)
        self.pitch_max_spin.setSuffix("x (最大)")
        
        pitch_layout.addWidget(self.pitch_min_spin)
        pitch_layout.addWidget(self.pitch_max_spin)
        audio_layout.addRow("🎵 音调范围:", pitch_layout)
        
        # 间隔时间范围
        interval_layout = QHBoxLayout()
        self.interval_min_spin = QDoubleSpinBox()
        self.interval_min_spin.setRange(0.1, 60.0)
        self.interval_min_spin.setSingleStep(0.5)
        self.interval_min_spin.setValue(1.0)
        self.interval_min_spin.setSuffix(" 秒 (最小)")
        
        self.interval_max_spin = QDoubleSpinBox()
        self.interval_max_spin.setRange(0.1, 60.0)
        self.interval_max_spin.setSingleStep(0.5)
        self.interval_max_spin.setValue(5.0)
        self.interval_max_spin.setSuffix(" 秒 (最大)")
        
        interval_layout.addWidget(self.interval_min_spin)
        interval_layout.addWidget(self.interval_max_spin)
        audio_layout.addRow("⏰ 间隔时间:", interval_layout)
        
        # 播放模式
        self.play_mode_combo = QComboBox()
        self.play_mode_combo.addItems(["完整播放", "片段播放"])
        audio_layout.addRow("🔄 播放模式:", self.play_mode_combo)
        
        # 片段播放时长
        self.segment_duration_spin = QSpinBox()
        self.segment_duration_spin.setRange(1, 60)
        self.segment_duration_spin.setValue(3)
        self.segment_duration_spin.setSuffix(" 秒")
        audio_layout.addRow("⏱️ 片段时长:", self.segment_duration_spin)
        
        layout.addWidget(audio_group)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        self.save_btn = QPushButton("💾 手动保存设置")
        self.save_btn.clicked.connect(self.save_settings)
        button_layout.addWidget(self.save_btn)
        
        self.load_btn = QPushButton("📂 重新加载设置")
        self.load_btn.clicked.connect(self.load_settings)
        button_layout.addWidget(self.load_btn)
        
        self.reset_btn = QPushButton("🔄 重置为默认值")
        self.reset_btn.clicked.connect(self.reset_to_defaults)
        button_layout.addWidget(self.reset_btn)
        
        layout.addLayout(button_layout)
        
        # 状态显示
        layout.addWidget(QLabel("📊 操作日志:"))
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)
        
        self.log("🚀 音频参数记忆功能测试程序已启动")
        
    def connect_signals(self):
        """连接信号"""
        # 连接所有参数变化信号到自动保存
        self.volume_min_spin.valueChanged.connect(self.auto_save)
        self.volume_max_spin.valueChanged.connect(self.auto_save)
        self.gain_min_spin.valueChanged.connect(self.auto_save)
        self.gain_max_spin.valueChanged.connect(self.auto_save)
        self.pitch_min_spin.valueChanged.connect(self.auto_save)
        self.pitch_max_spin.valueChanged.connect(self.auto_save)
        self.interval_min_spin.valueChanged.connect(self.auto_save)
        self.interval_max_spin.valueChanged.connect(self.auto_save)
        self.play_mode_combo.currentTextChanged.connect(self.auto_save)
        self.segment_duration_spin.valueChanged.connect(self.auto_save)
        
    def auto_save(self):
        """自动保存（参数变化时触发）"""
        self.save_settings()
        self.log("💾 参数已自动保存")
        
    def save_settings(self):
        """保存设置"""
        try:
            settings = QSettings("OBS去重工具", "AudioPlayer")
            
            # 保存音量范围
            settings.setValue("volume_min", self.volume_min_spin.value())
            settings.setValue("volume_max", self.volume_max_spin.value())
            
            # 保存增益范围
            settings.setValue("gain_min", self.gain_min_spin.value())
            settings.setValue("gain_max", self.gain_max_spin.value())
            
            # 保存音调范围
            settings.setValue("pitch_min", self.pitch_min_spin.value())
            settings.setValue("pitch_max", self.pitch_max_spin.value())
            
            # 保存间隔时间范围
            settings.setValue("interval_min", self.interval_min_spin.value())
            settings.setValue("interval_max", self.interval_max_spin.value())
            
            # 保存播放模式
            settings.setValue("play_mode", self.play_mode_combo.currentText())
            
            # 保存片段播放时长
            settings.setValue("segment_duration", self.segment_duration_spin.value())
            
            settings.sync()
            self.log("✅ 设置保存成功")
            
        except Exception as e:
            self.log(f"❌ 保存设置失败: {e}")
            
    def load_settings(self):
        """加载设置"""
        try:
            settings = QSettings("OBS去重工具", "AudioPlayer")
            
            # 加载音量范围
            self.volume_min_spin.setValue(settings.value("volume_min", 0.2, type=float))
            self.volume_max_spin.setValue(settings.value("volume_max", 0.8, type=float))
            
            # 加载增益范围
            self.gain_min_spin.setValue(settings.value("gain_min", -2.0, type=float))
            self.gain_max_spin.setValue(settings.value("gain_max", 2.0, type=float))
            
            # 加载音调范围
            self.pitch_min_spin.setValue(settings.value("pitch_min", 0.9, type=float))
            self.pitch_max_spin.setValue(settings.value("pitch_max", 1.1, type=float))
            
            # 加载间隔时间范围
            self.interval_min_spin.setValue(settings.value("interval_min", 1.0, type=float))
            self.interval_max_spin.setValue(settings.value("interval_max", 5.0, type=float))
            
            # 加载播放模式
            play_mode = settings.value("play_mode", "完整播放", type=str)
            index = self.play_mode_combo.findText(play_mode)
            if index >= 0:
                self.play_mode_combo.setCurrentIndex(index)
            
            # 加载片段播放时长
            self.segment_duration_spin.setValue(settings.value("segment_duration", 3, type=int))
            
            self.log("✅ 设置加载成功")
            
        except Exception as e:
            self.log(f"❌ 加载设置失败: {e}")
            
    def reset_to_defaults(self):
        """重置为默认值"""
        self.volume_min_spin.setValue(0.2)
        self.volume_max_spin.setValue(0.8)
        self.gain_min_spin.setValue(-2.0)
        self.gain_max_spin.setValue(2.0)
        self.pitch_min_spin.setValue(0.9)
        self.pitch_max_spin.setValue(1.1)
        self.interval_min_spin.setValue(1.0)
        self.interval_max_spin.setValue(5.0)
        self.play_mode_combo.setCurrentIndex(0)
        self.segment_duration_spin.setValue(3)
        
        self.log("🔄 已重置为默认值")
        
    def log(self, message):
        """记录日志"""
        self.log_text.append(message)
        cursor = self.log_text.textCursor()
        cursor.movePosition(cursor.End)
        self.log_text.setTextCursor(cursor)
        print(message)
        
    def closeEvent(self, event):
        """关闭事件"""
        self.save_settings()
        self.log("💾 程序关闭时已保存设置")
        event.accept()

def main():
    app = QApplication(sys.argv)
    window = AudioMemoryTest()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
