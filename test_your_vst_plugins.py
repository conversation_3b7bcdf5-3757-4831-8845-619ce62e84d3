# -*- coding: utf-8 -*-
"""
专门测试您的3个VST插件的脚本
Auburn Sounds Graillon 3-64, TAL-Reverb-4-64, TSE_808_2.0_x64
"""

import sys
import random
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QLineEdit, QDoubleSpinBox, QTextEdit,
    QGroupBox, QFormLayout, QCheckBox, QTabWidget, QComboBox
)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont

class YourVSTPluginsTest(QMainWindow):
    """您的VST插件测试"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_timers()
        
    def setup_ui(self):
        """设置界面"""
        self.setWindowTitle("🎛️ 您的VST插件测试 - Graillon + TSE808 + TAL混响")
        self.setGeometry(100, 100, 800, 700)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title = QLabel("🎛️ 您的专业VST插件控制测试")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 插件信息
        info_label = QLabel("Auburn Sounds Graillon 3-64 + TSE_808_2.0_x64 + TAL-Reverb-4-64")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("color: #666; font-size: 12pt; margin: 10px;")
        layout.addWidget(info_label)
        
        # 选项卡
        tab_widget = QTabWidget()
        
        # Graillon 音调控制选项卡
        graillon_tab = QWidget()
        graillon_layout = QFormLayout(graillon_tab)
        
        self.graillon_enabled_cb = QCheckBox("启用 Graillon 音调控制")
        self.graillon_enabled_cb.setStyleSheet("font-weight: bold; color: #8b5cf6;")
        self.graillon_enabled_cb.stateChanged.connect(self.toggle_graillon_control)
        graillon_layout.addRow(self.graillon_enabled_cb)
        
        self.graillon_filter_name_edit = QLineEdit("Graillon音调")
        graillon_layout.addRow("OBS滤镜名称:", self.graillon_filter_name_edit)
        
        # Graillon 参数设置
        graillon_params_group = QGroupBox("Graillon 3-64 参数设置")
        graillon_params_layout = QFormLayout(graillon_params_group)
        
        # 音调范围（更适合Graillon的范围）
        pitch_widget = QWidget()
        pitch_layout = QHBoxLayout(pitch_widget)
        
        self.graillon_pitch_min = QDoubleSpinBox()
        self.graillon_pitch_min.setRange(-12.0, 12.0)
        self.graillon_pitch_min.setValue(-6.0)
        self.graillon_pitch_min.setSuffix(" 半音")
        
        self.graillon_pitch_max = QDoubleSpinBox()
        self.graillon_pitch_max.setRange(-12.0, 12.0)
        self.graillon_pitch_max.setValue(6.0)
        self.graillon_pitch_max.setSuffix(" 半音")
        
        pitch_layout.addWidget(QLabel("最小:"))
        pitch_layout.addWidget(self.graillon_pitch_min)
        pitch_layout.addWidget(QLabel("最大:"))
        pitch_layout.addWidget(self.graillon_pitch_max)
        
        graillon_params_layout.addRow("🎵 音调范围:", pitch_widget)
        
        # Formant 控制（Graillon特有）
        formant_widget = QWidget()
        formant_layout = QHBoxLayout(formant_widget)
        
        self.graillon_formant_min = QDoubleSpinBox()
        self.graillon_formant_min.setRange(50.0, 150.0)
        self.graillon_formant_min.setValue(90.0)
        self.graillon_formant_min.setSuffix(" %")
        
        self.graillon_formant_max = QDoubleSpinBox()
        self.graillon_formant_max.setRange(50.0, 150.0)
        self.graillon_formant_max.setValue(110.0)
        self.graillon_formant_max.setSuffix(" %")
        
        formant_layout.addWidget(QLabel("最小:"))
        formant_layout.addWidget(self.graillon_formant_min)
        formant_layout.addWidget(QLabel("最大:"))
        formant_layout.addWidget(self.graillon_formant_max)
        
        graillon_params_layout.addRow("🎭 共振峰:", formant_widget)
        
        self.graillon_interval = QDoubleSpinBox()
        self.graillon_interval.setRange(1.0, 30.0)
        self.graillon_interval.setValue(3.0)
        self.graillon_interval.setSuffix(" 秒")
        graillon_params_layout.addRow("⏰ 变化间隔:", self.graillon_interval)
        
        graillon_layout.addRow(graillon_params_group)
        tab_widget.addTab(graillon_tab, "🎵 Graillon 3-64")
        
        # TSE 808 失真控制选项卡
        tse808_tab = QWidget()
        tse808_layout = QFormLayout(tse808_tab)
        
        self.tse808_enabled_cb = QCheckBox("启用 TSE 808 失真控制")
        self.tse808_enabled_cb.setStyleSheet("font-weight: bold; color: #ef4444;")
        self.tse808_enabled_cb.stateChanged.connect(self.toggle_tse808_control)
        tse808_layout.addRow(self.tse808_enabled_cb)
        
        self.tse808_filter_name_edit = QLineEdit("TSE808失真")
        tse808_layout.addRow("OBS滤镜名称:", self.tse808_filter_name_edit)
        
        # TSE 808 参数设置
        tse808_params_group = QGroupBox("TSE 808 参数设置")
        tse808_params_layout = QFormLayout(tse808_params_group)
        
        # Drive 控制
        drive_widget = QWidget()
        drive_layout = QHBoxLayout(drive_widget)
        
        self.tse808_drive_min = QDoubleSpinBox()
        self.tse808_drive_min.setRange(0.0, 100.0)
        self.tse808_drive_min.setValue(10.0)
        self.tse808_drive_min.setSuffix(" %")
        
        self.tse808_drive_max = QDoubleSpinBox()
        self.tse808_drive_max.setRange(0.0, 100.0)
        self.tse808_drive_max.setValue(70.0)
        self.tse808_drive_max.setSuffix(" %")
        
        drive_layout.addWidget(QLabel("最小:"))
        drive_layout.addWidget(self.tse808_drive_min)
        drive_layout.addWidget(QLabel("最大:"))
        drive_layout.addWidget(self.tse808_drive_max)
        
        tse808_params_layout.addRow("🔥 Drive 强度:", drive_widget)
        
        # Tone 控制
        tone_widget = QWidget()
        tone_layout = QHBoxLayout(tone_widget)
        
        self.tse808_tone_min = QDoubleSpinBox()
        self.tse808_tone_min.setRange(0.0, 100.0)
        self.tse808_tone_min.setValue(20.0)
        self.tse808_tone_min.setSuffix(" %")
        
        self.tse808_tone_max = QDoubleSpinBox()
        self.tse808_tone_max.setRange(0.0, 100.0)
        self.tse808_tone_max.setValue(80.0)
        self.tse808_tone_max.setSuffix(" %")
        
        tone_layout.addWidget(QLabel("最小:"))
        tone_layout.addWidget(self.tse808_tone_min)
        tone_layout.addWidget(QLabel("最大:"))
        tone_layout.addWidget(self.tse808_tone_max)
        
        tse808_params_layout.addRow("🎛️ Tone 音色:", tone_widget)
        
        self.tse808_interval = QDoubleSpinBox()
        self.tse808_interval.setRange(1.0, 30.0)
        self.tse808_interval.setValue(3.0)
        self.tse808_interval.setSuffix(" 秒")
        tse808_params_layout.addRow("⏰ 变化间隔:", self.tse808_interval)
        
        tse808_layout.addRow(tse808_params_group)
        tab_widget.addTab(tse808_tab, "🔥 TSE 808")
        
        # TAL 混响控制选项卡
        tal_tab = QWidget()
        tal_layout = QFormLayout(tal_tab)
        
        self.tal_enabled_cb = QCheckBox("启用 TAL 混响控制")
        self.tal_enabled_cb.setStyleSheet("font-weight: bold; color: #06b6d4;")
        self.tal_enabled_cb.stateChanged.connect(self.toggle_tal_control)
        tal_layout.addRow(self.tal_enabled_cb)
        
        self.tal_filter_name_edit = QLineEdit("TAL混响")
        tal_layout.addRow("OBS滤镜名称:", self.tal_filter_name_edit)
        
        # TAL 混响参数
        tal_params_group = QGroupBox("TAL Reverb 4 参数设置")
        tal_params_layout = QFormLayout(tal_params_group)
        
        # Room Size
        room_widget = QWidget()
        room_layout = QHBoxLayout(room_widget)
        
        self.tal_room_min = QDoubleSpinBox()
        self.tal_room_min.setRange(0.0, 100.0)
        self.tal_room_min.setValue(20.0)
        self.tal_room_min.setSuffix(" %")
        
        self.tal_room_max = QDoubleSpinBox()
        self.tal_room_max.setRange(0.0, 100.0)
        self.tal_room_max.setValue(60.0)
        self.tal_room_max.setSuffix(" %")
        
        room_layout.addWidget(QLabel("最小:"))
        room_layout.addWidget(self.tal_room_min)
        room_layout.addWidget(QLabel("最大:"))
        room_layout.addWidget(self.tal_room_max)
        
        tal_params_layout.addRow("🏠 房间大小:", room_widget)
        
        # Mix
        mix_widget = QWidget()
        mix_layout = QHBoxLayout(mix_widget)
        
        self.tal_mix_min = QDoubleSpinBox()
        self.tal_mix_min.setRange(0.0, 100.0)
        self.tal_mix_min.setValue(10.0)
        self.tal_mix_min.setSuffix(" %")
        
        self.tal_mix_max = QDoubleSpinBox()
        self.tal_mix_max.setRange(0.0, 100.0)
        self.tal_mix_max.setValue(40.0)
        self.tal_mix_max.setSuffix(" %")
        
        mix_layout.addWidget(QLabel("最小:"))
        mix_layout.addWidget(self.tal_mix_min)
        mix_layout.addWidget(QLabel("最大:"))
        mix_layout.addWidget(self.tal_mix_max)
        
        tal_params_layout.addRow("🌊 混响量:", mix_widget)
        
        self.tal_interval = QDoubleSpinBox()
        self.tal_interval.setRange(1.0, 30.0)
        self.tal_interval.setValue(5.0)
        self.tal_interval.setSuffix(" 秒")
        tal_params_layout.addRow("⏰ 变化间隔:", self.tal_interval)
        
        tal_layout.addRow(tal_params_group)
        tab_widget.addTab(tal_tab, "🌊 TAL Reverb")
        
        layout.addWidget(tab_widget)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        self.test_all_btn = QPushButton("🎛️ 测试所有插件")
        self.test_all_btn.clicked.connect(self.test_all_plugins)
        button_layout.addWidget(self.test_all_btn)
        
        self.stop_all_btn = QPushButton("⏹️ 停止所有控制")
        self.stop_all_btn.clicked.connect(self.stop_all_controls)
        button_layout.addWidget(self.stop_all_btn)
        
        layout.addLayout(button_layout)
        
        # 日志显示
        layout.addWidget(QLabel("📊 插件控制日志:"))
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)
        
        self.log("🚀 您的专业VST插件测试程序已启动")
        self.log("🎵 Graillon 3-64: 专业变声和音调处理")
        self.log("🔥 TSE 808: 经典失真和过载效果")
        self.log("🌊 TAL Reverb 4: 高质量混响处理")
        
    def setup_timers(self):
        """设置定时器"""
        self.graillon_timer = QTimer(self)
        self.tse808_timer = QTimer(self)
        self.tal_timer = QTimer(self)
        
    def toggle_graillon_control(self, state):
        """切换Graillon控制"""
        enabled = state == Qt.Checked
        if enabled:
            interval_ms = int(self.graillon_interval.value() * 1000)
            self.graillon_timer.timeout.connect(self.apply_graillon_effect)
            self.graillon_timer.start(interval_ms)
            self.log("✅ Graillon 3-64 音调控制已启用")
        else:
            self.graillon_timer.stop()
            self.graillon_timer.timeout.disconnect()
            self.log("⏹️ Graillon 3-64 音调控制已停用")
            
    def toggle_tse808_control(self, state):
        """切换TSE808控制"""
        enabled = state == Qt.Checked
        if enabled:
            interval_ms = int(self.tse808_interval.value() * 1000)
            self.tse808_timer.timeout.connect(self.apply_tse808_effect)
            self.tse808_timer.start(interval_ms)
            self.log("✅ TSE 808 失真控制已启用")
        else:
            self.tse808_timer.stop()
            self.tse808_timer.timeout.disconnect()
            self.log("⏹️ TSE 808 失真控制已停用")
            
    def toggle_tal_control(self, state):
        """切换TAL混响控制"""
        enabled = state == Qt.Checked
        if enabled:
            interval_ms = int(self.tal_interval.value() * 1000)
            self.tal_timer.timeout.connect(self.apply_tal_effect)
            self.tal_timer.start(interval_ms)
            self.log("✅ TAL Reverb 4 混响控制已启用")
        else:
            self.tal_timer.stop()
            self.tal_timer.timeout.disconnect()
            self.log("⏹️ TAL Reverb 4 混响控制已停用")
            
    def apply_graillon_effect(self):
        """应用Graillon效果"""
        pitch = random.uniform(self.graillon_pitch_min.value(), self.graillon_pitch_max.value())
        formant = random.uniform(self.graillon_formant_min.value(), self.graillon_formant_max.value())
        
        self.log(f"🎵 Graillon: 音调={pitch:.1f}半音, 共振峰={formant:.0f}%")
        
    def apply_tse808_effect(self):
        """应用TSE808效果"""
        drive = random.uniform(self.tse808_drive_min.value(), self.tse808_drive_max.value())
        tone = random.uniform(self.tse808_tone_min.value(), self.tse808_tone_max.value())
        
        self.log(f"🔥 TSE808: Drive={drive:.0f}%, Tone={tone:.0f}%")
        
    def apply_tal_effect(self):
        """应用TAL混响效果"""
        room = random.uniform(self.tal_room_min.value(), self.tal_room_max.value())
        mix = random.uniform(self.tal_mix_min.value(), self.tal_mix_max.value())
        
        self.log(f"🌊 TAL混响: 房间={room:.0f}%, 混响量={mix:.0f}%")
        
    def test_all_plugins(self):
        """测试所有插件"""
        self.apply_graillon_effect()
        self.apply_tse808_effect()
        self.apply_tal_effect()
        self.log("🎛️ 所有插件效果已应用")
        
    def stop_all_controls(self):
        """停止所有控制"""
        self.graillon_enabled_cb.setChecked(False)
        self.tse808_enabled_cb.setChecked(False)
        self.tal_enabled_cb.setChecked(False)
        self.log("⏹️ 所有VST控制已停止")
        
    def log(self, message):
        """记录日志"""
        self.log_text.append(message)
        cursor = self.log_text.textCursor()
        cursor.movePosition(cursor.End)
        self.log_text.setTextCursor(cursor)
        print(message)

def main():
    app = QApplication(sys.argv)
    window = YourVSTPluginsTest()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
