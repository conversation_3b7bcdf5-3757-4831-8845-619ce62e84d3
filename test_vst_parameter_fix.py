# -*- coding: utf-8 -*-
"""
测试VST参数检测修复
验证参数匹配逻辑是否正常工作
"""

def test_parameter_matching():
    """测试参数匹配逻辑"""
    print("🧪 测试VST参数匹配逻辑")
    print("=" * 50)
    
    # 模拟从OBS获取的VST滤镜设置
    mock_settings = {
        "drive": 30.0,
        "tone": 50.0, 
        "level": 80.0,
        "plugin_path": "C:\\Program Files\\VSTPlugins\\TSE_808_2.0_x64.dll"
    }
    
    print("📊 模拟的VST滤镜设置:")
    for key, value in mock_settings.items():
        print(f"  - {key}: {value}")
    print()
    
    # 测试参数查找
    test_params = ["drive", "tone", "level", "pitch", "formant", "mix"]
    
    for property_name in test_params:
        print(f"🔍 查找参数: '{property_name}'")
        
        # 直接匹配
        if property_name in mock_settings:
            value = mock_settings[property_name]
            print(f"  ✅ 直接找到: {value}")
            continue
        
        # 模糊匹配
        found = False
        for key, value in mock_settings.items():
            if property_name.lower() in key.lower():
                print(f"  ✅ 模糊匹配找到 '{key}': {value}")
                found = True
                break
        
        if not found:
            # 使用第一个数值参数
            for key, value in mock_settings.items():
                try:
                    float_value = float(value)
                    print(f"  ⚠️ 使用默认数值参数 '{key}': {float_value}")
                    break
                except (ValueError, TypeError):
                    continue
            else:
                print(f"  ❌ 未找到任何匹配参数")
        
        print()

def test_filter_detection():
    """测试滤镜检测逻辑"""
    print("🔍 测试VST滤镜检测逻辑")
    print("=" * 50)
    
    # 模拟从OBS获取的滤镜列表
    mock_filters = [
        {
            "filterName": "Graillon音调",
            "filterKind": "vst_filter",
            "filterEnabled": True
        },
        {
            "filterName": "TSE808失真", 
            "filterKind": "vst_filter",
            "filterEnabled": True
        },
        {
            "filterName": "TAL混响",
            "filterKind": "vst_filter", 
            "filterEnabled": True
        },
        {
            "filterName": "噪音抑制",
            "filterKind": "noise_suppress_filter",
            "filterEnabled": True
        }
    ]
    
    print("📋 模拟的滤镜列表:")
    for filter_info in mock_filters:
        filter_name = filter_info["filterName"]
        filter_kind = filter_info["filterKind"]
        print(f"  - 滤镜: '{filter_name}' (类型: {filter_kind})")
    print()
    
    # 测试滤镜查找
    test_filters = ["Graillon音调", "TSE808失真", "TAL混响", "不存在的滤镜"]
    
    for filter_name in test_filters:
        print(f"🔍 查找滤镜: '{filter_name}'")
        
        found = False
        for filter_info in mock_filters:
            filter_name_found = filter_info.get('filterName', '')
            filter_kind = filter_info.get('filterKind', '')
            
            if filter_name_found == filter_name:
                if filter_kind == 'vst_filter':
                    print(f"  ✅ 找到VST滤镜: '{filter_name}'")
                else:
                    print(f"  ⚠️ 找到滤镜但不是VST类型: '{filter_name}' ({filter_kind})")
                found = True
                break
        
        if not found:
            print(f"  ❌ 未找到滤镜: '{filter_name}'")
        
        print()

def test_vst_plugin_paths():
    """测试VST插件路径"""
    print("📁 测试VST插件路径")
    print("=" * 50)
    
    import os
    
    plugin_paths = {
        "Graillon 3-64": "C:\\Program Files\\VSTPlugins\\Auburn Sounds Graillon 3-64.dll",
        "TSE 808": "C:\\Program Files\\VSTPlugins\\TSE_808_2.0_x64.dll",
        "TAL Reverb": "C:\\Program Files\\VSTPlugins\\TAL-Reverb-4-64.dll"
    }
    
    for name, path in plugin_paths.items():
        exists = os.path.exists(path)
        status = "✅ 存在" if exists else "❌ 不存在"
        print(f"  {name}: {status}")
        print(f"    路径: {path}")
        print()

def main():
    print("🔧 VST参数检测修复测试")
    print("=" * 60)
    print()
    
    test_parameter_matching()
    print()
    
    test_filter_detection()
    print()
    
    test_vst_plugin_paths()
    print()
    
    print("📋 修复总结:")
    print("1. ✅ 改进了参数匹配逻辑 - 支持直接匹配和模糊匹配")
    print("2. ✅ 添加了滤镜创建后的等待时间")
    print("3. ✅ 改进了错误处理 - 即使检测失败也继续执行")
    print("4. ✅ 优化了参数设置逻辑 - 智能参数名称匹配")
    print()
    print("🎯 现在VST控制功能应该可以正常工作了！")
    print("请在主程序中测试VST音调和失真控制功能。")

if __name__ == "__main__":
    main()
