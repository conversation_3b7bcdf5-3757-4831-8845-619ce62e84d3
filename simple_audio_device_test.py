# -*- coding: utf-8 -*-
"""
简单音频设备测试 - 基于您提供的C++示例
直接验证音频是否输出到指定设备
"""

import sys
import os
import numpy as np
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QComboBox, QPushButton, QLabel, QTextEdit, QMessageBox
)
from PyQt5.QtCore import Qt, QIODevice, QByteArray, QBuffer
from PyQt5.QtMultimedia import QAudioDeviceInfo, QAudio, QAudioOutput, QAudioFormat

try:
    import soundfile as sf
    AUDIO_PROCESSING_AVAILABLE = True
except ImportError:
    AUDIO_PROCESSING_AVAILABLE = False

class SimpleAudioDeviceTest(QMainWindow):
    """简单音频设备测试"""
    
    def __init__(self):
        super().__init__()
        self.selected_device = None
        self.audio_output = None
        self.audio_buffer = None
        self.setup_ui()
        self.populate_devices()
        
    def setup_ui(self):
        """设置界面"""
        self.setWindowTitle("🔊 简单音频设备测试")
        self.setGeometry(100, 100, 500, 400)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 设备选择
        layout.addWidget(QLabel("选择音频输出设备:"))
        self.device_combo = QComboBox()
        self.device_combo.currentTextChanged.connect(self.on_device_changed)
        layout.addWidget(self.device_combo)
        
        # 按钮
        button_layout = QHBoxLayout()
        
        self.test_btn = QPushButton("🎵 播放测试音频")
        self.test_btn.clicked.connect(self.play_test_audio)
        button_layout.addWidget(self.test_btn)
        
        self.stop_btn = QPushButton("⏹️ 停止播放")
        self.stop_btn.clicked.connect(self.stop_audio)
        button_layout.addWidget(self.stop_btn)
        
        layout.addLayout(button_layout)
        
        # 状态显示
        layout.addWidget(QLabel("状态信息:"))
        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(200)
        self.status_text.setReadOnly(True)
        layout.addWidget(self.status_text)
        
    def populate_devices(self):
        """填充设备列表"""
        self.device_combo.clear()
        self.log("🔍 检测音频输出设备...")
        
        try:
            # 获取所有可用的音频输出设备（类似C++示例）
            device_list = QAudioDeviceInfo.availableDevices(QAudio.AudioOutput)
            
            if not device_list:
                self.log("❌ 未找到音频输出设备")
                self.device_combo.addItem("未找到设备", None)
                return
            
            # 添加设备到列表
            for i, device in enumerate(device_list):
                if not device.isNull():
                    device_name = device.deviceName()
                    display_name = f"设备 {i+1}: {device_name}"
                    self.device_combo.addItem(display_name, device)
                    self.log(f"✅ 找到设备: {device_name}")
            
            # 默认选择第一个设备（类似C++示例中的first()）
            if self.device_combo.count() > 0:
                self.device_combo.setCurrentIndex(0)
                self.log(f"📌 默认选择第一个设备")
                
        except Exception as e:
            self.log(f"❌ 检测设备时出错: {e}")
            
    def on_device_changed(self, device_name):
        """设备选择改变"""
        self.selected_device = self.device_combo.currentData()
        if self.selected_device and not self.selected_device.isNull():
            self.log(f"🎯 已选择设备: {self.selected_device.deviceName()}")
        else:
            self.log("⚠️ 未选择有效设备")
            
    def play_test_audio(self):
        """播放测试音频 - 基于C++示例的逻辑"""
        try:
            if not self.selected_device:
                self.log("❌ 请先选择音频输出设备")
                return
                
            self.log(f"🎵 开始播放测试音频到: {self.selected_device.deviceName()}")
            
            # 生成测试音频数据（2秒440Hz正弦波）
            duration = 2.0
            sample_rate = 44100
            frequency = 440.0
            
            t = np.linspace(0, duration, int(sample_rate * duration), False)
            audio_data = 0.3 * np.sin(2 * np.pi * frequency * t)
            
            # 转换为立体声
            stereo_audio = np.column_stack((audio_data, audio_data))
            
            # 转换为16位整数（类似C++中的音频格式）
            audio_data_int16 = (stereo_audio * 32767).astype(np.int16)
            audio_bytes = audio_data_int16.tobytes()
            
            self.log(f"📊 生成音频数据: {len(audio_bytes)} 字节")
            
            # 🔑 关键：完全按照C++示例的方式
            # 1. 创建QAudioOutput对象并设置设备信息
            self.audio_output = QAudioOutput(self.selected_device)
            
            # 2. 准备音频数据（类似C++中从文件读取）
            self.audio_buffer = QBuffer()
            self.audio_buffer.setData(QByteArray(audio_bytes))
            self.audio_buffer.open(QIODevice.ReadOnly)
            
            # 3. 开始播放音频数据（类似C++中的start()调用）
            self.audio_output.start(self.audio_buffer)
            
            # 检查状态
            state = self.audio_output.state()
            error = self.audio_output.error()
            
            self.log(f"🔊 播放状态: {state}")
            self.log(f"🔊 错误状态: {error}")
            
            if state == QAudio.ActiveState:
                self.log("✅ 音频播放已开始！")
                self.log("🎧 请检查指定设备是否有声音输出")
            else:
                self.log(f"❌ 播放失败，状态: {state}")
                
        except Exception as e:
            self.log(f"❌ 播放测试音频失败: {e}")
            import traceback
            traceback.print_exc()
            
    def stop_audio(self):
        """停止音频播放"""
        try:
            if self.audio_output:
                self.audio_output.stop()
                self.log("⏹️ 音频播放已停止")
            else:
                self.log("ℹ️ 当前没有音频在播放")
                
            if self.audio_buffer:
                self.audio_buffer.close()
                self.audio_buffer = None
                
        except Exception as e:
            self.log(f"❌ 停止播放时出错: {e}")
            
    def log(self, message):
        """记录日志"""
        self.status_text.append(message)
        # 自动滚动到底部
        cursor = self.status_text.textCursor()
        cursor.movePosition(cursor.End)
        self.status_text.setTextCursor(cursor)
        
        # 同时打印到控制台
        print(message)

def main():
    app = QApplication(sys.argv)
    
    # 显示说明
    msg = QMessageBox()
    msg.setWindowTitle("使用说明")
    msg.setText("""
🔊 简单音频设备测试工具

使用步骤：
1. 选择要测试的音频输出设备
2. 点击"播放测试音频"按钮
3. 检查指定设备是否有声音输出
4. 如果没有声音，请检查设备连接和系统设置

注意：
- 这个测试直接基于您提供的C++示例
- 如果仍然没有声音，可能是系统级别的问题
    """)
    msg.exec_()
    
    window = SimpleAudioDeviceTest()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
