# -*- coding: utf-8 -*-
"""
测试VST混响控制功能
验证TAL-Reverb-4-64混响控制是否正常工作
"""

import sys
import random
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QDoubleSpinBox, QTextEdit, QCheckBox,
    QGroupBox, QFormLayout, QLineEdit
)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont

class VSTReverbControlTest(QMainWindow):
    """VST混响控制测试"""
    
    def __init__(self):
        super().__init__()
        # 模拟VST混响控制状态
        self.vst_reverb_control = {
            "enabled": False,
            "source_name": "麦克风",
            "filter_name": "TAL混响",
            "min_roomsize": 20.0,
            "max_roomsize": 80.0,
            "min_damping": 30.0,
            "max_damping": 80.0,
            "min_mix": 10.0,
            "max_mix": 50.0,
            "interval_secs": 5.0,
            "timer": QTimer(self),
            "original_roomsize": 40.0,
            "original_damping": 60.0,
            "original_mix": 25.0
        }
        self.setup_ui()
        
    def setup_ui(self):
        """设置界面"""
        self.setWindowTitle("🌊 VST混响控制测试")
        self.setGeometry(100, 100, 700, 600)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title = QLabel("🌊 VST混响控制功能测试")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 说明
        info = QLabel("测试TAL-Reverb-4-64混响插件的自动控制功能")
        info.setAlignment(Qt.AlignCenter)
        info.setStyleSheet("color: #666; margin: 10px;")
        layout.addWidget(info)
        
        # 控制设置
        control_group = QGroupBox("🎛️ 混响控制设置")
        control_layout = QFormLayout(control_group)
        
        # 启用控制
        self.reverb_enabled_cb = QCheckBox("启用VST混响控制")
        self.reverb_enabled_cb.setStyleSheet("font-weight: bold; color: #06b6d4;")
        self.reverb_enabled_cb.stateChanged.connect(self.toggle_reverb_control)
        control_layout.addRow(self.reverb_enabled_cb)
        
        # 滤镜名称
        self.filter_name_edit = QLineEdit("TAL混响")
        control_layout.addRow("滤镜名称:", self.filter_name_edit)
        
        # 房间大小范围
        roomsize_layout = QHBoxLayout()
        self.roomsize_min_spin = QDoubleSpinBox()
        self.roomsize_min_spin.setRange(0.0, 100.0)
        self.roomsize_min_spin.setValue(20.0)
        self.roomsize_min_spin.setSuffix(" %")
        
        self.roomsize_max_spin = QDoubleSpinBox()
        self.roomsize_max_spin.setRange(0.0, 100.0)
        self.roomsize_max_spin.setValue(80.0)
        self.roomsize_max_spin.setSuffix(" %")
        
        roomsize_layout.addWidget(QLabel("最小:"))
        roomsize_layout.addWidget(self.roomsize_min_spin)
        roomsize_layout.addWidget(QLabel("最大:"))
        roomsize_layout.addWidget(self.roomsize_max_spin)
        
        control_layout.addRow("🏠 房间大小:", roomsize_layout)
        
        # 阻尼范围
        damping_layout = QHBoxLayout()
        self.damping_min_spin = QDoubleSpinBox()
        self.damping_min_spin.setRange(0.0, 100.0)
        self.damping_min_spin.setValue(30.0)
        self.damping_min_spin.setSuffix(" %")
        
        self.damping_max_spin = QDoubleSpinBox()
        self.damping_max_spin.setRange(0.0, 100.0)
        self.damping_max_spin.setValue(80.0)
        self.damping_max_spin.setSuffix(" %")
        
        damping_layout.addWidget(QLabel("最小:"))
        damping_layout.addWidget(self.damping_min_spin)
        damping_layout.addWidget(QLabel("最大:"))
        damping_layout.addWidget(self.damping_max_spin)
        
        control_layout.addRow("🔧 阻尼控制:", damping_layout)
        
        # 混响量范围
        mix_layout = QHBoxLayout()
        self.mix_min_spin = QDoubleSpinBox()
        self.mix_min_spin.setRange(0.0, 100.0)
        self.mix_min_spin.setValue(10.0)
        self.mix_min_spin.setSuffix(" %")
        
        self.mix_max_spin = QDoubleSpinBox()
        self.mix_max_spin.setRange(0.0, 100.0)
        self.mix_max_spin.setValue(50.0)
        self.mix_max_spin.setSuffix(" %")
        
        mix_layout.addWidget(QLabel("最小:"))
        mix_layout.addWidget(self.mix_min_spin)
        mix_layout.addWidget(QLabel("最大:"))
        mix_layout.addWidget(self.mix_max_spin)
        
        control_layout.addRow("🌊 混响量:", mix_layout)
        
        # 变化间隔
        self.interval_spin = QDoubleSpinBox()
        self.interval_spin.setRange(1.0, 60.0)
        self.interval_spin.setValue(5.0)
        self.interval_spin.setSuffix(" 秒")
        control_layout.addRow("⏰ 变化间隔:", self.interval_spin)
        
        layout.addWidget(control_group)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        self.test_single_btn = QPushButton("🧪 测试单次变化")
        self.test_single_btn.clicked.connect(self.test_single_change)
        button_layout.addWidget(self.test_single_btn)
        
        self.reset_btn = QPushButton("🔄 重置参数")
        self.reset_btn.clicked.connect(self.reset_parameters)
        button_layout.addWidget(self.reset_btn)
        
        layout.addLayout(button_layout)
        
        # 当前参数显示
        current_group = QGroupBox("📊 当前混响参数")
        current_layout = QFormLayout(current_group)
        
        self.current_roomsize_label = QLabel("40.0%")
        current_layout.addRow("房间大小:", self.current_roomsize_label)
        
        self.current_damping_label = QLabel("60.0%")
        current_layout.addRow("阻尼:", self.current_damping_label)
        
        self.current_mix_label = QLabel("25.0%")
        current_layout.addRow("混响量:", self.current_mix_label)
        
        layout.addWidget(current_group)
        
        # 日志显示
        layout.addWidget(QLabel("📋 控制日志:"))
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)
        
        self.log("🚀 VST混响控制测试程序已启动")
        self.log("🌊 TAL-Reverb-4-64 混响插件控制测试")
        
    def toggle_reverb_control(self, state):
        """切换混响控制"""
        enabled = state == Qt.Checked
        self.vst_reverb_control["enabled"] = enabled
        
        if enabled:
            interval_ms = int(self.interval_spin.value() * 1000)
            self.vst_reverb_control["timer"].timeout.connect(self.apply_random_reverb)
            self.vst_reverb_control["timer"].start(interval_ms)
            self.log("✅ VST混响控制已启用")
            self.log(f"⏰ 每 {self.interval_spin.value()} 秒自动调整混响参数")
        else:
            self.vst_reverb_control["timer"].stop()
            self.vst_reverb_control["timer"].timeout.disconnect()
            self.reset_parameters()
            self.log("⏹️ VST混响控制已停用")
            
    def apply_random_reverb(self):
        """应用随机混响变化"""
        min_roomsize = self.roomsize_min_spin.value()
        max_roomsize = self.roomsize_max_spin.value()
        min_damping = self.damping_min_spin.value()
        max_damping = self.damping_max_spin.value()
        min_mix = self.mix_min_spin.value()
        max_mix = self.mix_max_spin.value()
        
        random_roomsize = random.uniform(min_roomsize, max_roomsize)
        random_damping = random.uniform(min_damping, max_damping)
        random_mix = random.uniform(min_mix, max_mix)
        
        # 更新显示
        self.current_roomsize_label.setText(f"{random_roomsize:.1f}%")
        self.current_damping_label.setText(f"{random_damping:.1f}%")
        self.current_mix_label.setText(f"{random_mix:.1f}%")
        
        # 记录日志
        self.log(f"🌊 混响参数已调整:")
        self.log(f"  - 房间大小: {random_roomsize:.1f}%")
        self.log(f"  - 阻尼: {random_damping:.1f}%")
        self.log(f"  - 混响量: {random_mix:.1f}%")
        
    def test_single_change(self):
        """测试单次变化"""
        self.apply_random_reverb()
        self.log("🧪 执行单次混响参数变化")
        
    def reset_parameters(self):
        """重置参数"""
        self.current_roomsize_label.setText("40.0%")
        self.current_damping_label.setText("60.0%")
        self.current_mix_label.setText("25.0%")
        self.log("🔄 混响参数已重置为默认值")
        
    def log(self, message):
        """记录日志"""
        self.log_text.append(message)
        cursor = self.log_text.textCursor()
        cursor.movePosition(cursor.End)
        self.log_text.setTextCursor(cursor)
        print(message)
        
    def closeEvent(self, event):
        """关闭事件"""
        if self.vst_reverb_control["timer"].isActive():
            self.vst_reverb_control["timer"].stop()
        event.accept()

def main():
    app = QApplication(sys.argv)
    window = VSTReverbControlTest()
    window.show()
    
    # 显示功能说明
    window.log("=" * 50)
    window.log("🌊 VST混响控制功能说明:")
    window.log("1. 房间大小 - 控制混响空间的大小感")
    window.log("2. 阻尼控制 - 调节混响的衰减特性")
    window.log("3. 混响量 - 设置干湿信号的比例")
    window.log("4. 变化间隔 - 混响参数自动变化的时间间隔")
    window.log("=" * 50)
    window.log("✅ 现在您有完整的3个VST控制功能:")
    window.log("  🎵 Graillon音调控制")
    window.log("  🔥 TSE808失真控制") 
    window.log("  🌊 TAL混响控制")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
