# 🎛️ VST插件设置指南

## 📋 概述

现在您的OBS去重工具已经支持VST音调和失真控制功能！通过在OBS中添加VST滤镜，您可以实现：

- 🎵 **VST音调控制**：自动调节音频的音调（升调/降调）
- 🔥 **VST失真控制**：自动调节音频的失真效果

## 🔧 VST插件准备

### 1. VST插件位置
您提到的VST插件位置：
```
C:\Program Files\VSTPlugins\
├── plugin1.dll
├── plugin2.dll
└── plugin3.dll
```

### 2. 推荐的VST插件

#### 音调控制插件
- **Pitch Shifter**：专门用于音调调节
- **Harmonizer**：和声器，包含音调功能
- **Voice Changer**：变声器，通常包含音调控制

#### 失真控制插件
- **Distortion**：经典失真效果器
- **Overdrive**：过载失真
- **Tube Amp**：电子管放大器模拟

## 🎯 OBS中的VST设置

### 步骤1：添加VST滤镜

1. **选择音频源**：
   - 在OBS中右键点击您的音频源（如麦克风、音频输入等）
   - 选择"滤镜"

2. **添加VST滤镜**：
   - 点击"+"按钮
   - 选择"VST 2.x 插件"
   - 给滤镜命名（重要！）

3. **选择VST插件**：
   - 在插件路径中选择您的VST插件
   - 配置插件参数

### 步骤2：滤镜命名规范

#### 音调控制滤镜
```
滤镜名称：VSR2X音调
或者：VST音调
或者：Pitch Control
```

#### 失真控制滤镜
```
滤镜名称：VSR2X失真
或者：VST失真
或者：Distortion Control
```

**⚠️ 重要**：滤镜名称必须与程序中设置的名称完全一致！

## 🎛️ 程序中的设置

### VST音调控制设置

1. **启用控制**：勾选"启用VST音调控制"
2. **滤镜名称**：输入在OBS中设置的滤镜名称（如"VSR2X音调"）
3. **音调范围**：
   - 最小值：-12 半音（降低一个八度）
   - 最大值：+12 半音（升高一个八度）
4. **变化间隔**：设置音调变化的时间间隔（秒）

### VST失真控制设置

1. **启用控制**：勾选"启用VST失真控制"
2. **滤镜名称**：输入在OBS中设置的滤镜名称（如"VSR2X失真"）
3. **失真参数**：
   - 失真强度：0-100%
   - 音色调节：0-100%
4. **变化间隔**：设置失真变化的时间间隔（秒）

## 🔍 VST参数映射

### 常见VST参数名称

#### 音调控制参数
```
- pitch：音调偏移（半音）
- semitones：半音数
- cents：音分（1半音=100音分）
- transpose：移调
```

#### 失真控制参数
```
- drive：失真强度/驱动
- gain：增益
- tone：音色/音调
- level：输出电平
- mix：干湿比
```

## 🧪 测试步骤

### 1. 基本测试

1. **设置音频源**：
   - 在程序中选择对应的音频源
   - 确保OBS中该源已添加VST滤镜

2. **测试音调控制**：
   - 启用VST音调控制
   - 设置较短的变化间隔（如2秒）
   - 观察音频音调是否发生变化

3. **测试失真控制**：
   - 启用VST失真控制
   - 设置失真参数范围
   - 观察音频失真效果是否变化

### 2. 故障排除

#### 问题1：提示"VST滤镜不存在"
**解决方案**：
- 检查滤镜名称是否完全一致
- 确认滤镜已正确添加到音频源
- 重新启动OBS和程序

#### 问题2：参数不生效
**解决方案**：
- 检查VST插件是否支持对应参数
- 尝试手动调节VST插件确认参数名称
- 查看控制台错误信息

#### 问题3：音频质量下降
**解决方案**：
- 调整VST插件的质量设置
- 减小参数变化范围
- 增加变化间隔时间

## 📊 推荐设置

### 音调控制推荐设置
```
音调范围：-3 到 +3 半音
变化间隔：3-5 秒
适用场景：语音变化、音乐效果
```

### 失真控制推荐设置
```
失真强度：10-50%
音色调节：30-70%
变化间隔：2-4 秒
适用场景：游戏音效、特殊效果
```

## 🔧 高级配置

### 自定义参数映射

如果您的VST插件使用不同的参数名称，可以修改程序代码：

```python
# 在 apply_random_vst_pitch 方法中
# 将 "pitch" 替换为您的VST插件的参数名
success = self.set_vst_filter_property(source_name, filter_name, "your_param_name", random_pitch)
```

### 多个VST滤镜

您可以为同一个音频源添加多个VST滤镜：
- 一个用于音调控制
- 一个用于失真控制
- 其他效果滤镜

## 🎯 使用场景

### 直播场景
- **游戏直播**：增加音效变化，提升观众体验
- **聊天直播**：声音变化增加趣味性
- **音乐直播**：实时音效处理

### 录制场景
- **视频制作**：自动化音频后期处理
- **播客录制**：声音效果增强
- **教学视频**：重点强调音频变化

## 📋 注意事项

1. **性能影响**：VST插件会消耗CPU资源，注意监控系统性能
2. **音频延迟**：某些VST插件可能引入音频延迟
3. **兼容性**：确保VST插件与您的系统兼容
4. **备份设置**：重要的VST设置建议备份

现在您可以享受自动化的VST音调和失真控制功能了！🎉
