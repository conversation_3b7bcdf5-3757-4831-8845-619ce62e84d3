# 🔧 VST滤镜自动设置完整指南

## 🎯 概述

现在您的程序已经支持**完全自动化**的VST滤镜设置！无需手动在OBS中添加滤镜，程序会自动为您完成所有操作。

## ✨ 新增功能

### 🔧 自动VST滤镜设置
- ✅ **自动检测**：程序启动时自动检测VST滤镜是否存在
- ✅ **自动创建**：如果滤镜不存在，自动创建VST 2.x滤镜
- ✅ **自动配置**：自动设置插件路径和默认参数
- ✅ **智能管理**：避免重复创建，智能处理现有滤镜

### 🎛️ 一键设置按钮
- ✅ **手动触发**：在VST音调控制tab中添加了"自动设置VST滤镜"按钮
- ✅ **确认对话框**：显示将要创建的滤镜列表
- ✅ **状态反馈**：详细的成功/失败提示

## 🚀 使用方法

### 方法1：自动触发（推荐）

1. **选择音频源**：
   ```
   在程序中选择您的音频媒体源（如麦克风）
   ```

2. **启用VST控制**：
   ```
   勾选"启用VST音调控制"或"启用VST失真控制"
   ```

3. **自动设置**：
   ```
   程序检测到滤镜不存在时，会自动提示并创建所有VST滤镜
   ```

### 方法2：手动触发

1. **点击自动设置按钮**：
   ```
   在"VST音调控制"tab中点击"🔧 自动设置VST滤镜"按钮
   ```

2. **确认设置**：
   ```
   在弹出的确认对话框中点击"是"
   ```

3. **等待完成**：
   ```
   程序会自动创建所有VST滤镜并配置默认参数
   ```

## 🎛️ 自动创建的滤镜

程序会自动创建以下3个VST滤镜：

### 1. Graillon音调滤镜
```
滤镜名称：Graillon音调
插件路径：C:\Program Files\VSTPlugins\Auburn Sounds Graillon 3-64.dll
默认参数：
  - pitch: 0.0 (无音调偏移)
  - formant: 100.0 (自然共振峰)
  - mix: 100.0 (全湿信号)
```

### 2. TSE808失真滤镜
```
滤镜名称：TSE808失真
插件路径：C:\Program Files\VSTPlugins\TSE_808_2.0_x64.dll
默认参数：
  - drive: 30.0 (中等失真强度)
  - tone: 50.0 (平衡音色)
  - level: 80.0 (输出电平)
```

### 3. TAL混响滤镜
```
滤镜名称：TAL混响
插件路径：C:\Program Files\VSTPlugins\TAL-Reverb-4-64.dll
默认参数：
  - roomsize: 40.0 (中等房间大小)
  - damping: 60.0 (适度阻尼)
  - mix: 25.0 (轻微混响)
```

## ⚙️ 配置选项

### VST插件路径配置

如果您的VST插件不在默认路径，可以使用配置工具：

```bash
python vst_auto_setup_tool.py
```

**配置工具功能**：
- 🔍 **自动检测**：扫描常见路径查找VST插件
- 📁 **手动选择**：浏览文件选择插件位置
- 🧪 **路径测试**：验证插件文件是否存在
- 💾 **保存配置**：保存自定义路径设置

### 自定义插件路径

在程序代码中修改插件路径：

```python
# 在 auto_setup_vst_filters 方法中修改
vst_plugins = {
    "Graillon音调": {
        "plugin_path": "您的Graillon插件路径.dll",
        "default_settings": {...}
    },
    "TSE808失真": {
        "plugin_path": "您的TSE808插件路径.dll", 
        "default_settings": {...}
    },
    "TAL混响": {
        "plugin_path": "您的TAL混响插件路径.dll",
        "default_settings": {...}
    }
}
```

## 🔍 工作流程

### 自动设置流程

```
1. 用户启用VST控制
   ↓
2. 程序检查滤镜是否存在
   ↓
3. 如果不存在 → 自动创建滤镜
   ↓
4. 设置插件路径和默认参数
   ↓
5. 验证滤镜创建成功
   ↓
6. 开始VST参数控制
```

### 错误处理流程

```
插件文件不存在
   ↓
显示错误提示
   ↓
建议用户检查插件路径
   ↓
提供配置工具链接
```

## 🧪 测试验证

### 验证步骤

1. **检查OBS滤镜列表**：
   ```
   在OBS中查看音频源的滤镜列表
   应该看到新创建的3个VST滤镜
   ```

2. **测试滤镜功能**：
   ```
   在OBS中手动调节VST插件参数
   确认音频效果确实发生变化
   ```

3. **测试程序控制**：
   ```
   启用VST控制功能
   观察参数是否自动变化
   ```

### 预期结果

✅ **自动创建成功**：
```
🔧 开始为音频源 '麦克风' 自动设置VST滤镜...
🔧 创建VST滤镜: 'Graillon音调'
✅ VST滤镜 'Graillon音调' 创建成功
🔧 创建VST滤镜: 'TSE808失真'
✅ VST滤镜 'TSE808失真' 创建成功
🔧 创建VST滤镜: 'TAL混响'
✅ VST滤镜 'TAL混响' 创建成功
✅ 自动设置完成：成功创建 3/3 个VST滤镜
```

## 🔧 故障排除

### 常见问题

#### 问题1：插件文件不存在
**错误信息**：
```
❌ VST滤镜 'Graillon音调' 创建失败
```

**解决方案**：
1. 检查插件文件是否存在于指定路径
2. 使用配置工具重新设置路径
3. 确认插件文件名称正确

#### 问题2：OBS权限问题
**错误信息**：
```
❌ 创建VST滤镜时出错: 权限被拒绝
```

**解决方案**：
1. 以管理员身份运行OBS
2. 检查OBS WebSocket连接状态
3. 重启OBS和程序

#### 问题3：滤镜已存在
**提示信息**：
```
ℹ️ VST滤镜 'Graillon音调' 已存在，跳过创建
```

**说明**：这是正常情况，程序会跳过已存在的滤镜

### 高级故障排除

#### 检查插件兼容性
```python
# 在配置工具中测试插件路径
python vst_auto_setup_tool.py
# 点击"测试路径"按钮
```

#### 手动清理滤镜
```python
# 如果需要重新创建滤镜，可以先删除现有滤镜
self.remove_vst_filter(source_name, filter_name)
```

## 🎉 使用体验

### 现在您可以：

1. ✅ **零手动操作**：完全不需要在OBS中手动添加滤镜
2. ✅ **一键设置**：点击按钮即可完成所有配置
3. ✅ **智能管理**：自动检测和避免重复创建
4. ✅ **即插即用**：启用功能时自动设置所需滤镜

### 典型使用场景

```
第一次使用：
1. 选择音频源：麦克风
2. 启用VST音调控制
3. 程序自动检测并创建所有VST滤镜
4. 立即开始享受自动音调和失真控制

日常使用：
1. 启动程序
2. 直接启用VST控制功能
3. 程序检测到滤镜已存在，直接开始控制
```

现在您拥有了完全自动化的VST滤镜管理系统！🎛️✨

无需任何手动操作，程序会自动为您处理所有VST滤镜的创建、配置和管理。只需要确保VST插件文件在正确的路径下，其余的一切都由程序自动完成！
