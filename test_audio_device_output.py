# -*- coding: utf-8 -*-
"""
音频设备输出测试脚本
用于验证音频是否正确输出到指定的声卡设备
"""

import sys
import os
import numpy as np
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QComboBox, QPushButton, QLabel, QSlider, QTextEdit, QGroupBox,
    QSpinBox, QMessageBox
)
from PyQt5.QtCore import Qt, QTimer, QIODevice
from PyQt5.QtMultimedia import QAudioDeviceInfo, QAudio, QAudioOutput, QAudioFormat

try:
    import soundfile as sf
    AUDIO_PROCESSING_AVAILABLE = True
except ImportError:
    AUDIO_PROCESSING_AVAILABLE = False
    print("⚠️ soundfile库未安装，将使用生成的测试音频")

class AudioDeviceTestWindow(QMainWindow):
    """音频设备测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.selected_device = None
        self.current_audio_output = None
        self.setup_ui()
        self.populate_devices()
        
    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("🔊 音频设备输出测试工具")
        self.setGeometry(100, 100, 600, 500)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 设备选择组
        device_group = QGroupBox("🎯 音频输出设备选择")
        device_layout = QVBoxLayout(device_group)
        
        self.device_combo = QComboBox()
        self.device_combo.currentTextChanged.connect(self.on_device_changed)
        device_layout.addWidget(QLabel("选择音频输出设备:"))
        device_layout.addWidget(self.device_combo)
        
        refresh_btn = QPushButton("🔄 刷新设备列表")
        refresh_btn.clicked.connect(self.populate_devices)
        device_layout.addWidget(refresh_btn)
        
        layout.addWidget(device_group)
        
        # 测试控制组
        test_group = QGroupBox("🧪 音频测试")
        test_layout = QVBoxLayout(test_group)
        
        # 音量控制
        volume_layout = QHBoxLayout()
        volume_layout.addWidget(QLabel("音量:"))
        self.volume_slider = QSlider(Qt.Horizontal)
        self.volume_slider.setRange(0, 100)
        self.volume_slider.setValue(50)
        self.volume_label = QLabel("50%")
        self.volume_slider.valueChanged.connect(
            lambda v: self.volume_label.setText(f"{v}%")
        )
        volume_layout.addWidget(self.volume_slider)
        volume_layout.addWidget(self.volume_label)
        test_layout.addLayout(volume_layout)
        
        # 频率控制
        freq_layout = QHBoxLayout()
        freq_layout.addWidget(QLabel("测试频率 (Hz):"))
        self.freq_spin = QSpinBox()
        self.freq_spin.setRange(100, 2000)
        self.freq_spin.setValue(440)
        freq_layout.addWidget(self.freq_spin)
        freq_layout.addStretch()
        test_layout.addLayout(freq_layout)
        
        # 测试按钮
        button_layout = QHBoxLayout()
        
        self.test_left_btn = QPushButton("🔊 测试左声道")
        self.test_left_btn.clicked.connect(lambda: self.test_channel("left"))
        button_layout.addWidget(self.test_left_btn)
        
        self.test_right_btn = QPushButton("🔊 测试右声道")
        self.test_right_btn.clicked.connect(lambda: self.test_channel("right"))
        button_layout.addWidget(self.test_right_btn)
        
        self.test_stereo_btn = QPushButton("🔊 测试立体声")
        self.test_stereo_btn.clicked.connect(lambda: self.test_channel("stereo"))
        button_layout.addWidget(self.test_stereo_btn)
        
        test_layout.addLayout(button_layout)
        
        self.stop_btn = QPushButton("⏹️ 停止播放")
        self.stop_btn.clicked.connect(self.stop_audio)
        test_layout.addWidget(self.stop_btn)
        
        layout.addWidget(test_group)
        
        # 状态显示
        status_group = QGroupBox("📊 状态信息")
        status_layout = QVBoxLayout(status_group)
        
        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(150)
        self.status_text.setReadOnly(True)
        status_layout.addWidget(self.status_text)
        
        layout.addWidget(status_group)
        
    def populate_devices(self):
        """填充音频设备列表"""
        self.device_combo.clear()
        self.log("🔍 正在检测音频输出设备...")
        
        try:
            # 添加默认设备
            default_device = QAudioDeviceInfo.defaultOutputDevice()
            if not default_device.isNull():
                self.device_combo.addItem(f"🔊 {default_device.deviceName()} (默认)", default_device)
                self.log(f"✅ 找到默认设备: {default_device.deviceName()}")
            
            # 添加所有可用设备
            devices = QAudioDeviceInfo.availableDevices(QAudio.AudioOutput)
            device_names = set()
            
            for device in devices:
                device_name = device.deviceName()
                if device_name not in device_names and not device.isNull():
                    display_name = f"🎵 {device_name}"
                    self.device_combo.addItem(display_name, device)
                    device_names.add(device_name)
                    self.log(f"✅ 找到设备: {device_name}")
            
            if self.device_combo.count() == 0:
                self.device_combo.addItem("❌ 未找到音频设备", None)
                self.log("❌ 未找到任何音频输出设备")
            else:
                self.log(f"✅ 总共找到 {self.device_combo.count()} 个音频输出设备")
                
        except Exception as e:
            self.log(f"❌ 检测音频设备时出错: {e}")
            
    def on_device_changed(self, device_name):
        """设备选择改变"""
        self.selected_device = self.device_combo.currentData()
        if self.selected_device and not self.selected_device.isNull():
            self.log(f"🎯 已选择设备: {self.selected_device.deviceName()}")
            self.show_device_info()
        else:
            self.log("⚠️ 未选择有效设备")
            
    def show_device_info(self):
        """显示设备信息"""
        if not self.selected_device:
            return
            
        try:
            preferred_format = self.selected_device.preferredFormat()
            self.log(f"📋 设备信息:")
            self.log(f"  - 设备名称: {self.selected_device.deviceName()}")
            self.log(f"  - 首选采样率: {preferred_format.sampleRate()} Hz")
            self.log(f"  - 首选声道数: {preferred_format.channelCount()}")
            self.log(f"  - 首选采样大小: {preferred_format.sampleSize()} bits")
            self.log(f"  - 编解码器: {preferred_format.codec()}")
            
        except Exception as e:
            self.log(f"❌ 获取设备信息失败: {e}")
            
    def test_channel(self, channel_type):
        """测试指定声道"""
        if not self.selected_device:
            self.log("⚠️ 请先选择音频输出设备")
            return
            
        self.log(f"🧪 开始测试 {channel_type} 声道...")
        
        try:
            # 生成测试音频
            duration = 2.0  # 2秒
            sample_rate = 44100
            frequency = self.freq_spin.value()
            
            t = np.linspace(0, duration, int(sample_rate * duration), False)
            audio_data = 0.3 * np.sin(2 * np.pi * frequency * t)
            
            # 根据声道类型处理音频
            if channel_type == "left":
                # 仅左声道
                stereo_audio = np.column_stack((audio_data, np.zeros_like(audio_data)))
            elif channel_type == "right":
                # 仅右声道
                stereo_audio = np.column_stack((np.zeros_like(audio_data), audio_data))
            else:  # stereo
                # 立体声
                stereo_audio = np.column_stack((audio_data, audio_data))
            
            # 播放测试音频
            success = self.play_audio_through_device(stereo_audio, sample_rate)
            
            if success:
                self.log(f"✅ {channel_type} 声道测试播放成功 ({frequency}Hz)")
            else:
                self.log(f"❌ {channel_type} 声道测试播放失败")
                
        except Exception as e:
            self.log(f"❌ 声道测试失败: {e}")
            
    def play_audio_through_device(self, audio_data, sample_rate):
        """通过指定设备播放音频"""
        try:
            if not self.selected_device:
                return False
                
            # 停止之前的播放
            if self.current_audio_output:
                self.current_audio_output.stop()
                
            # 设置音频格式
            format = QAudioFormat()
            format.setSampleRate(sample_rate)
            format.setChannelCount(2)  # 立体声
            format.setSampleSize(16)
            format.setCodec("audio/pcm")
            format.setByteOrder(QAudioFormat.LittleEndian)
            format.setSampleType(QAudioFormat.SignedInt)
            
            # 检查设备是否支持该格式
            if not self.selected_device.isFormatSupported(format):
                self.log("⚠️ 设备不支持指定格式，使用最接近格式")
                format = self.selected_device.nearestFormat(format)
                
            # 转换音频数据为16位整数
            audio_data_int16 = (audio_data * 32767).astype(np.int16)
            
            # 创建音频输出
            self.current_audio_output = QAudioOutput(self.selected_device, format)
            
            # 设置音量
            volume_linear = self.volume_slider.value() / 100.0
            self.current_audio_output.setVolume(volume_linear)
            
            # 创建音频数据的字节流
            audio_bytes = audio_data_int16.tobytes()
            
            # 开始播放
            io_device = self.current_audio_output.start()
            if io_device:
                bytes_written = io_device.write(audio_bytes)
                self.log(f"📤 写入音频数据: {bytes_written} 字节")
                return bytes_written > 0
            else:
                self.log("❌ 无法启动音频输出")
                return False
                
        except Exception as e:
            self.log(f"❌ 音频播放失败: {e}")
            return False
            
    def stop_audio(self):
        """停止音频播放"""
        if self.current_audio_output:
            self.current_audio_output.stop()
            self.current_audio_output = None
            self.log("⏹️ 音频播放已停止")
        else:
            self.log("ℹ️ 当前没有音频在播放")
            
    def log(self, message):
        """记录日志信息"""
        self.status_text.append(message)
        # 自动滚动到底部
        cursor = self.status_text.textCursor()
        cursor.movePosition(cursor.End)
        self.status_text.setTextCursor(cursor)

def main():
    app = QApplication(sys.argv)
    
    # 检查音频处理库
    if not AUDIO_PROCESSING_AVAILABLE:
        QMessageBox.warning(None, "警告", 
                          "soundfile库未安装，将使用生成的测试音频。\n"
                          "要获得完整功能，请安装: pip install soundfile")
    
    window = AudioDeviceTestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
