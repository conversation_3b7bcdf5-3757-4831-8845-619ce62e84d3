# -*- coding: utf-8 -*-
"""
VST集成功能测试
测试新添加的VST自动化功能是否正常工作
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QTextEdit, QLabel
from PyQt5.QtCore import Qt, QTimer
import random

class VSTIntegrationTest(QMainWindow):
    """VST集成功能测试"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("VST集成功能测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 模拟主程序的VST控制状态
        self.vst_pitch_control = {
            "enabled": False,
            "source_name": "测试音频源",
            "filter_name": "Graillon音调",
            "min_pitch": -6.0,
            "max_pitch": 6.0,
            "interval_secs": 3.0,
            "timer": QTimer(self),
            "original_pitch": 0.0
        }
        
        self.vst_distortion_control = {
            "enabled": False,
            "source_name": "测试音频源",
            "filter_name": "TSE808失真",
            "min_drive": 10.0,
            "max_drive": 70.0,
            "min_tone": 20.0,
            "max_tone": 80.0,
            "interval_secs": 3.0,
            "timer": QTimer(self),
            "original_drive": 30.0,
            "original_tone": 50.0
        }
        
        self.is_connected = True  # 模拟已连接状态
        
        self.setup_ui()
        self.setup_timers()
        
    def setup_ui(self):
        """设置UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title = QLabel("🎛️ VST集成功能测试")
        title.setStyleSheet("""
            QLabel {
                font-size: 18pt;
                font-weight: bold;
                color: #2563eb;
                padding: 15px;
                background: #f0f9ff;
                border-radius: 8px;
                border: 2px solid #93c5fd;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 控制按钮
        self.pitch_btn = QPushButton("🎵 启动VST音调控制")
        self.pitch_btn.setStyleSheet(self.get_button_style("#10b981"))
        self.pitch_btn.clicked.connect(self.toggle_pitch_control)
        layout.addWidget(self.pitch_btn)
        
        self.distortion_btn = QPushButton("🔥 启动VST失真控制")
        self.distortion_btn.setStyleSheet(self.get_button_style("#f59e0b"))
        self.distortion_btn.clicked.connect(self.toggle_distortion_control)
        layout.addWidget(self.distortion_btn)
        
        self.test_all_btn = QPushButton("🚀 测试所有VST功能")
        self.test_all_btn.setStyleSheet(self.get_button_style("#8b5cf6"))
        self.test_all_btn.clicked.connect(self.test_all_functions)
        layout.addWidget(self.test_all_btn)
        
        self.stop_all_btn = QPushButton("⏹️ 停止所有测试")
        self.stop_all_btn.setStyleSheet(self.get_button_style("#ef4444"))
        self.stop_all_btn.clicked.connect(self.stop_all_tests)
        layout.addWidget(self.stop_all_btn)
        
        # 日志显示
        log_label = QLabel("📋 测试日志：")
        log_label.setStyleSheet("font-weight: bold; font-size: 12pt; color: #374151;")
        layout.addWidget(log_label)
        
        self.log_text = QTextEdit()
        self.log_text.setStyleSheet("""
            QTextEdit {
                background: #f9fafb;
                border: 2px solid #e5e7eb;
                border-radius: 8px;
                padding: 10px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 10pt;
            }
        """)
        layout.addWidget(self.log_text)
        
        # 初始日志
        self.log("🎛️ VST集成功能测试已启动")
        self.log("📋 测试环境：")
        self.log(f"  - VST音调控制：{self.vst_pitch_control['filter_name']}")
        self.log(f"  - VST失真控制：{self.vst_distortion_control['filter_name']}")
        self.log(f"  - 音频源：{self.vst_pitch_control['source_name']}")
        self.log("")
        
    def setup_timers(self):
        """设置定时器"""
        self.vst_pitch_control["timer"].timeout.connect(self.apply_random_vst_pitch)
        self.vst_distortion_control["timer"].timeout.connect(self.apply_random_vst_distortion)
        
    def get_button_style(self, color):
        """获取按钮样式"""
        return f"""
            QPushButton {{
                background: {color};
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 8px;
                font-size: 12pt;
                font-weight: bold;
                min-height: 20px;
            }}
            QPushButton:hover {{
                background: {color}dd;
                transform: translateY(-1px);
            }}
            QPushButton:pressed {{
                background: {color}bb;
                transform: translateY(0px);
            }}
        """
        
    def toggle_pitch_control(self):
        """切换音调控制"""
        if not self.vst_pitch_control["enabled"]:
            self.vst_pitch_control["enabled"] = True
            interval_ms = int(self.vst_pitch_control["interval_secs"] * 1000)
            self.vst_pitch_control["timer"].start(interval_ms)
            self.pitch_btn.setText("🎵 停止VST音调控制")
            self.pitch_btn.setStyleSheet(self.get_button_style("#ef4444"))
            self.log("✅ VST音调控制已启动")
        else:
            self.vst_pitch_control["enabled"] = False
            self.vst_pitch_control["timer"].stop()
            self.pitch_btn.setText("🎵 启动VST音调控制")
            self.pitch_btn.setStyleSheet(self.get_button_style("#10b981"))
            self.log("⏹️ VST音调控制已停止")
            
    def toggle_distortion_control(self):
        """切换失真控制"""
        if not self.vst_distortion_control["enabled"]:
            self.vst_distortion_control["enabled"] = True
            interval_ms = int(self.vst_distortion_control["interval_secs"] * 1000)
            self.vst_distortion_control["timer"].start(interval_ms)
            self.distortion_btn.setText("🔥 停止VST失真控制")
            self.distortion_btn.setStyleSheet(self.get_button_style("#ef4444"))
            self.log("✅ VST失真控制已启动")
        else:
            self.vst_distortion_control["enabled"] = False
            self.vst_distortion_control["timer"].stop()
            self.distortion_btn.setText("🔥 启动VST失真控制")
            self.distortion_btn.setStyleSheet(self.get_button_style("#f59e0b"))
            self.log("⏹️ VST失真控制已停止")
            
    def apply_random_vst_pitch(self):
        """应用随机VST音调变化"""
        if not self.vst_pitch_control["enabled"]:
            return
            
        min_pitch = self.vst_pitch_control["min_pitch"]
        max_pitch = self.vst_pitch_control["max_pitch"]
        random_pitch = random.uniform(min_pitch, max_pitch)
        
        source_name = self.vst_pitch_control["source_name"]
        filter_name = self.vst_pitch_control["filter_name"]
        
        self.log(f"🎵 VST音调调整: {source_name} -> {filter_name} -> {random_pitch:.1f} 半音")
        
        # 模拟设置VST参数
        self.simulate_vst_parameter_setting(source_name, filter_name, "pitch", random_pitch)
        
    def apply_random_vst_distortion(self):
        """应用随机VST失真变化"""
        if not self.vst_distortion_control["enabled"]:
            return
            
        min_drive = self.vst_distortion_control["min_drive"]
        max_drive = self.vst_distortion_control["max_drive"]
        min_tone = self.vst_distortion_control["min_tone"]
        max_tone = self.vst_distortion_control["max_tone"]
        
        random_drive = random.uniform(min_drive, max_drive)
        random_tone = random.uniform(min_tone, max_tone)
        
        source_name = self.vst_distortion_control["source_name"]
        filter_name = self.vst_distortion_control["filter_name"]
        
        self.log(f"🔥 VST失真调整: {source_name} -> {filter_name} -> Drive:{random_drive:.1f}%, Tone:{random_tone:.1f}%")
        
        # 模拟设置VST参数
        self.simulate_vst_parameter_setting(source_name, filter_name, "drive", random_drive)
        self.simulate_vst_parameter_setting(source_name, filter_name, "tone", random_tone)
        
    def simulate_vst_parameter_setting(self, source_name, filter_name, param_name, value):
        """模拟VST参数设置"""
        self.log(f"  📊 设置参数: {param_name} = {value:.1f}")
        
    def test_all_functions(self):
        """测试所有VST功能"""
        self.log("🚀 开始测试所有VST功能...")
        
        # 启动音调控制
        if not self.vst_pitch_control["enabled"]:
            self.toggle_pitch_control()
            
        # 启动失真控制
        if not self.vst_distortion_control["enabled"]:
            self.toggle_distortion_control()
            
        self.log("✅ 所有VST功能已启动，正在自动测试...")
        
    def stop_all_tests(self):
        """停止所有测试"""
        self.log("⏹️ 停止所有VST测试...")
        
        if self.vst_pitch_control["enabled"]:
            self.toggle_pitch_control()
            
        if self.vst_distortion_control["enabled"]:
            self.toggle_distortion_control()
            
        self.log("✅ 所有VST测试已停止")
        
    def log(self, message):
        """记录日志"""
        self.log_text.append(message)
        cursor = self.log_text.textCursor()
        cursor.movePosition(cursor.End)
        self.log_text.setTextCursor(cursor)
        print(message)

def main():
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyleSheet("""
        QMainWindow {
            background-color: #ffffff;
        }
        QWidget {
            font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
        }
    """)
    
    window = VSTIntegrationTest()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
