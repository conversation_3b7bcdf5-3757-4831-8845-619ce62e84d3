#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频系统测试脚本
验证音频设备管理系统的各项功能
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QTextEdit
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtMultimedia import QAudioDeviceInfo, QAudio
import numpy as np

# 导入我们的音频组件
try:
    from modern_audio_device_panel import ModernAudioDevicePanel
    from audio_device_manager import AudioDeviceManager
    COMPONENTS_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 组件导入失败: {e}")
    COMPONENTS_AVAILABLE = False

try:
    import soundfile as sf
    AUDIO_PROCESSING_AVAILABLE = True
except ImportError:
    AUDIO_PROCESSING_AVAILABLE = False

class AudioSystemTester(QWidget):
    """音频系统测试器"""
    
    def __init__(self):
        super().__init__()
        self.test_results = []
        self.setup_ui()
        self.run_all_tests()
        
    def setup_ui(self):
        """设置测试界面"""
        self.setWindowTitle("🧪 音频系统测试器")
        self.setGeometry(200, 200, 800, 600)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        
        # 标题
        title = QLabel("🧪 音频设备管理系统测试")
        title.setStyleSheet("""
            QLabel {
                font-size: 18pt;
                font-weight: bold;
                color: #667eea;
                padding: 15px;
                background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                border-radius: 10px;
                border: 2px solid #e2e8f0;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 测试按钮区域
        button_widget = QWidget()
        button_layout = QHBoxLayout(button_widget)
        
        self.test_basic_btn = QPushButton("🔍 基础功能测试")
        self.test_basic_btn.clicked.connect(self.test_basic_functions)
        
        self.test_ui_btn = QPushButton("🎨 UI组件测试")
        self.test_ui_btn.clicked.connect(self.test_ui_components)
        
        self.test_audio_btn = QPushButton("🔊 音频功能测试")
        self.test_audio_btn.clicked.connect(self.test_audio_functions)
        
        self.test_integration_btn = QPushButton("🔗 集成测试")
        self.test_integration_btn.clicked.connect(self.test_integration)
        
        for btn in [self.test_basic_btn, self.test_ui_btn, self.test_audio_btn, self.test_integration_btn]:
            btn.setStyleSheet("""
                QPushButton {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 12px 20px;
                    font-weight: bold;
                    font-size: 11pt;
                }
                QPushButton:hover {
                    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
                    transform: translateY(-2px);
                }
            """)
            button_layout.addWidget(btn)
            
        layout.addWidget(button_widget)
        
        # 测试结果显示
        self.result_text = QTextEdit()
        self.result_text.setStyleSheet("""
            QTextEdit {
                border: 2px solid #e2e8f0;
                border-radius: 8px;
                background: #f8fafc;
                padding: 15px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 10pt;
                color: #1e293b;
            }
        """)
        self.result_text.setReadOnly(True)
        layout.addWidget(self.result_text)
        
        # 底部状态栏
        self.status_label = QLabel("✅ 测试器已就绪")
        self.status_label.setStyleSheet("""
            QLabel {
                background: #f1f5f9;
                border: 1px solid #e2e8f0;
                border-radius: 6px;
                padding: 8px 15px;
                color: #64748b;
                font-weight: 500;
            }
        """)
        layout.addWidget(self.status_label)
        
    def log_test(self, message, success=True):
        """记录测试结果"""
        icon = "✅" if success else "❌"
        timestamp = time.strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {icon} {message}"
        
        self.result_text.append(log_message)
        self.test_results.append((message, success))
        
        # 自动滚动到底部
        cursor = self.result_text.textCursor()
        cursor.movePosition(cursor.End)
        self.result_text.setTextCursor(cursor)
        
        # 更新状态
        if success:
            self.status_label.setText(f"✅ {message}")
        else:
            self.status_label.setText(f"❌ {message}")
            
        # 刷新界面
        QApplication.processEvents()
        
    def run_all_tests(self):
        """运行所有测试"""
        self.log_test("开始音频系统测试", True)
        
        # 环境检查
        self.test_environment()
        
    def test_environment(self):
        """测试环境检查"""
        self.log_test("=== 环境检查 ===", True)
        
        # 检查Python版本
        python_version = sys.version_info
        if python_version >= (3, 7):
            self.log_test(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}", True)
        else:
            self.log_test(f"Python版本过低: {python_version.major}.{python_version.minor}.{python_version.micro}", False)
            
        # 检查PyQt5
        try:
            from PyQt5.QtCore import QT_VERSION_STR
            self.log_test(f"PyQt5版本: {QT_VERSION_STR}", True)
        except ImportError:
            self.log_test("PyQt5未安装", False)
            
        # 检查音频处理库
        if AUDIO_PROCESSING_AVAILABLE:
            self.log_test("soundfile库: 可用", True)
        else:
            self.log_test("soundfile库: 不可用 (某些功能受限)", False)
            
        # 检查组件可用性
        if COMPONENTS_AVAILABLE:
            self.log_test("音频组件: 可用", True)
        else:
            self.log_test("音频组件: 不可用", False)
            
    def test_basic_functions(self):
        """测试基础功能"""
        self.log_test("=== 基础功能测试 ===", True)
        
        # 测试音频设备检测
        try:
            devices = QAudioDeviceInfo.availableDevices(QAudio.AudioOutput)
            device_count = len(devices)
            
            if device_count > 0:
                self.log_test(f"检测到 {device_count} 个音频输出设备", True)
                
                # 列出设备信息
                for i, device in enumerate(devices[:3]):  # 只显示前3个设备
                    if not device.isNull():
                        device_name = device.deviceName()
                        self.log_test(f"  设备 {i+1}: {device_name}", True)
                        
                        # 获取设备详细信息
                        try:
                            preferred_format = device.preferredFormat()
                            sample_rate = preferred_format.sampleRate()
                            channels = preferred_format.channelCount()
                            sample_size = preferred_format.sampleSize()
                            
                            self.log_test(f"    格式: {sample_rate}Hz, {channels}声道, {sample_size}bit", True)
                        except Exception as e:
                            self.log_test(f"    获取格式信息失败: {e}", False)
                            
            else:
                self.log_test("未检测到音频输出设备", False)
                
        except Exception as e:
            self.log_test(f"音频设备检测失败: {e}", False)
            
        # 测试音频格式支持
        try:
            from PyQt5.QtMultimedia import QAudioFormat
            
            format = QAudioFormat()
            format.setSampleRate(44100)
            format.setChannelCount(2)
            format.setSampleSize(16)
            format.setCodec("audio/pcm")
            format.setByteOrder(QAudioFormat.LittleEndian)
            format.setSampleType(QAudioFormat.SignedInt)
            
            self.log_test("音频格式创建: 成功", True)
            
        except Exception as e:
            self.log_test(f"音频格式创建失败: {e}", False)
            
    def test_ui_components(self):
        """测试UI组件"""
        self.log_test("=== UI组件测试 ===", True)
        
        if not COMPONENTS_AVAILABLE:
            self.log_test("组件不可用，跳过UI测试", False)
            return
            
        # 测试音频设备面板创建
        try:
            panel = ModernAudioDevicePanel()
            self.log_test("音频设备面板创建: 成功", True)
            
            # 测试面板方法
            try:
                panel.load_audio_devices()
                self.log_test("设备加载方法: 成功", True)
            except Exception as e:
                self.log_test(f"设备加载方法失败: {e}", False)
                
            try:
                config = panel.get_current_config()
                self.log_test("配置获取方法: 成功", True)
                self.log_test(f"  当前配置: {config}", True)
            except Exception as e:
                self.log_test(f"配置获取方法失败: {e}", False)
                
            # 清理
            panel.deleteLater()
            
        except Exception as e:
            self.log_test(f"音频设备面板创建失败: {e}", False)
            
        # 测试设备管理器创建
        try:
            manager = AudioDeviceManager()
            self.log_test("设备管理器创建: 成功", True)
            
            # 不显示窗口，只测试创建
            manager.deleteLater()
            
        except Exception as e:
            self.log_test(f"设备管理器创建失败: {e}", False)
            
    def test_audio_functions(self):
        """测试音频功能"""
        self.log_test("=== 音频功能测试 ===", True)
        
        # 测试音频数据生成
        try:
            # 生成测试音调
            duration = 1.0
            sample_rate = 44100
            frequency = 440.0
            
            t = np.linspace(0, duration, int(sample_rate * duration), False)
            audio_data = 0.3 * np.sin(2 * np.pi * frequency * t)
            
            self.log_test(f"音频数据生成: 成功 ({len(audio_data)} 采样点)", True)
            
            # 测试立体声转换
            stereo_audio = np.column_stack((audio_data, audio_data))
            self.log_test(f"立体声转换: 成功 ({stereo_audio.shape})", True)
            
        except Exception as e:
            self.log_test(f"音频数据生成失败: {e}", False)
            
        # 测试声道处理
        try:
            # 模拟声道处理
            test_audio = np.random.random((1000, 2))  # 1000个立体声采样点
            
            # 仅左声道
            left_only = test_audio.copy()
            left_only[:, 1] = 0
            self.log_test("左声道处理: 成功", True)
            
            # 仅右声道
            right_only = test_audio.copy()
            right_only[:, 0] = 0
            self.log_test("右声道处理: 成功", True)
            
            # 平衡调节
            balanced = test_audio.copy()
            balance_factor = 0.5  # 右偏50%
            balanced[:, 0] *= (1 - balance_factor)
            self.log_test("平衡调节: 成功", True)
            
        except Exception as e:
            self.log_test(f"声道处理失败: {e}", False)
            
        # 测试音频输出创建
        try:
            from PyQt5.QtMultimedia import QAudioOutput, QAudioFormat
            
            # 获取默认设备
            default_device = QAudioDeviceInfo.defaultOutputDevice()
            
            if not default_device.isNull():
                # 创建音频格式
                format = QAudioFormat()
                format.setSampleRate(44100)
                format.setChannelCount(2)
                format.setSampleSize(16)
                format.setCodec("audio/pcm")
                format.setByteOrder(QAudioFormat.LittleEndian)
                format.setSampleType(QAudioFormat.SignedInt)
                
                # 创建音频输出
                audio_output = QAudioOutput(default_device, format)
                self.log_test("音频输出创建: 成功", True)
                
                # 测试音量设置
                audio_output.setVolume(0.5)
                volume = audio_output.volume()
                self.log_test(f"音量设置: 成功 (当前音量: {volume})", True)
                
                # 清理
                audio_output.stop()
                
            else:
                self.log_test("默认音频设备不可用", False)
                
        except Exception as e:
            self.log_test(f"音频输出测试失败: {e}", False)
            
    def test_integration(self):
        """集成测试"""
        self.log_test("=== 集成测试 ===", True)
        
        if not COMPONENTS_AVAILABLE:
            self.log_test("组件不可用，跳过集成测试", False)
            return
            
        # 测试完整的音频播放流程
        try:
            # 创建设备面板
            panel = ModernAudioDevicePanel()
            
            # 获取配置
            config = panel.get_current_config()
            device = config.get('device')
            
            if device:
                self.log_test(f"集成测试设备: {device.deviceName()}", True)
                
                # 生成测试音频
                duration = 0.5  # 短时间测试
                sample_rate = 44100
                frequency = 440.0
                
                t = np.linspace(0, duration, int(sample_rate * duration), False)
                audio_data = 0.1 * np.sin(2 * np.pi * frequency * t)  # 低音量
                stereo_audio = np.column_stack((audio_data, audio_data))
                
                # 应用声道处理
                processed_audio = panel.apply_channel_processing(stereo_audio)
                
                self.log_test("音频处理流程: 成功", True)
                
                # 注意：这里不实际播放音频，只测试处理流程
                self.log_test("集成测试完成 (未播放音频)", True)
                
            else:
                self.log_test("集成测试: 使用系统默认设备", True)
                
            # 清理
            panel.deleteLater()
            
        except Exception as e:
            self.log_test(f"集成测试失败: {e}", False)
            
        # 生成测试报告
        self.generate_test_report()
        
    def generate_test_report(self):
        """生成测试报告"""
        self.log_test("=== 测试报告 ===", True)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for _, success in self.test_results if success)
        failed_tests = total_tests - passed_tests
        
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        self.log_test(f"总测试数: {total_tests}", True)
        self.log_test(f"通过测试: {passed_tests}", True)
        self.log_test(f"失败测试: {failed_tests}", failed_tests == 0)
        self.log_test(f"成功率: {success_rate:.1f}%", success_rate >= 80)
        
        if success_rate >= 90:
            self.log_test("🎉 测试结果: 优秀", True)
        elif success_rate >= 80:
            self.log_test("👍 测试结果: 良好", True)
        elif success_rate >= 60:
            self.log_test("⚠️ 测试结果: 一般", False)
        else:
            self.log_test("❌ 测试结果: 需要改进", False)
            
        # 保存测试报告到文件
        try:
            report_file = "audio_system_test_report.txt"
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("音频设备管理系统测试报告\n")
                f.write("=" * 40 + "\n")
                f.write(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"总测试数: {total_tests}\n")
                f.write(f"通过测试: {passed_tests}\n")
                f.write(f"失败测试: {failed_tests}\n")
                f.write(f"成功率: {success_rate:.1f}%\n\n")
                
                f.write("详细测试结果:\n")
                f.write("-" * 20 + "\n")
                for message, success in self.test_results:
                    status = "PASS" if success else "FAIL"
                    f.write(f"[{status}] {message}\n")
                    
            self.log_test(f"测试报告已保存: {report_file}", True)
            
        except Exception as e:
            self.log_test(f"保存测试报告失败: {e}", False)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("音频系统测试器")
    app.setApplicationVersion("1.0")
    
    # 创建测试器
    tester = AudioSystemTester()
    tester.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
