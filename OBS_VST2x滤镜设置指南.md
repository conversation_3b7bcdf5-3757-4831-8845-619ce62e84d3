# 🎛️ OBS VST 2.x滤镜设置指南

## 📋 概述

本指南将帮助您在OBS中正确设置VST 2.x滤镜，并与我们的程序进行联动控制。

## 🔧 OBS中的VST 2.x滤镜设置

### 步骤1：添加VST 2.x滤镜

1. **选择音频源**：
   - 在OBS中右键点击您的音频源（如麦克风、桌面音频等）
   - 选择"滤镜"

2. **添加VST 2.x插件滤镜**：
   - 点击"+"按钮
   - 选择"VST 2.x 插件"
   - **重要**：给滤镜起一个容易识别的名称

3. **选择VST插件**：
   - 在"插件"下拉菜单中选择您的VST插件
   - 配置插件的初始参数

### 步骤2：为您的3个插件设置滤镜

#### Auburn Sounds Graillon 3-64 (音调控制)

```
滤镜类型：VST 2.x 插件
滤镜名称：Graillon音调
插件选择：Auburn Sounds Graillon 3-64
```

**初始参数建议**：
- Pitch: 0 semitones (默认)
- Formant: 100% (自然)
- Mix: 100% (全湿信号)

#### TSE_808_2.0_x64 (失真控制)

```
滤镜类型：VST 2.x 插件
滤镜名称：TSE808失真
插件选择：TSE_808_2.0_x64
```

**初始参数建议**：
- Drive: 30% (中等失真)
- Tone: 50% (平衡音色)
- Level: 80% (输出电平)

#### TAL-Reverb-4-64 (混响控制)

```
滤镜类型：VST 2.x 插件
滤镜名称：TAL混响
插件选择：TAL-Reverb-4-64
```

**初始参数建议**：
- Room Size: 40% (中等房间)
- Damping: 60% (适度阻尼)
- Mix: 25% (轻微混响)

## 🎯 程序中的对应设置

### VST音调控制设置

```
启用VST音调控制：✅
VST滤镜名称：Graillon音调
音调偏移范围：
  - 最小：-6.0 半音
  - 最大：6.0 半音
变化间隔：3.0 秒
```

### VST失真控制设置

```
启用VST失真控制：✅
VST滤镜名称：TSE808失真
失真强度范围：
  - 最小：10.0 %
  - 最大：70.0 %
音色调节范围：
  - 最小：20.0 %
  - 最大：80.0 %
变化间隔：3.0 秒
```

## 🔍 VST参数名称映射

### 常见的VST参数名称格式

OBS中的VST 2.x插件参数可能以不同格式存储：

#### Auburn Sounds Graillon 3-64
```
可能的参数名称：
- pitch / Pitch / PITCH
- semitones / Semitones
- formant / Formant
- mix / Mix
```

#### TSE_808_2.0_x64
```
可能的参数名称：
- drive / Drive / DRIVE
- tone / Tone / TONE
- level / Level / LEVEL
- gain / Gain
```

#### TAL-Reverb-4-64
```
可能的参数名称：
- roomsize / RoomSize / room_size
- damping / Damping
- mix / Mix / wet
- predelay / PreDelay
```

## 🧪 测试和调试

### 步骤1：验证滤镜设置

1. **检查滤镜是否正确添加**：
   - 在OBS中确认滤镜已添加到正确的音频源
   - 滤镜名称与程序中设置的完全一致

2. **测试滤镜功能**：
   - 在OBS中手动调节VST插件参数
   - 确认音频效果确实发生变化

### 步骤2：程序连接测试

1. **启用调试模式**：
   - 程序现在会显示详细的调试信息
   - 查看控制台输出了解参数检测情况

2. **检查参数映射**：
   ```
   控制台会显示：
   🔍 VST滤镜 'Graillon音调' 当前设置:
     - pitch: 0.0
     - formant: 100.0
     - mix: 100.0
   ✅ 找到参数 'pitch': 0.0
   ```

3. **测试参数控制**：
   - 启用VST控制功能
   - 观察控制台输出和音频变化

### 步骤3：故障排除

#### 问题1：提示"未找到滤镜"
**解决方案**：
- 检查滤镜名称是否完全一致（区分大小写）
- 确认滤镜类型是"VST 2.x 插件"
- 重新启动OBS和程序

#### 问题2：参数不生效
**解决方案**：
- 查看控制台的参数列表
- 尝试不同的参数名称格式
- 检查VST插件是否支持自动化

#### 问题3：音频质量问题
**解决方案**：
- 调整VST插件的质量设置
- 检查音频采样率设置
- 减小参数变化范围

## 🎛️ 高级设置

### 自定义参数映射

如果程序无法自动识别参数，您可以：

1. **查看控制台输出**：
   ```
   🔍 VST滤镜 'Graillon音调' 当前设置:
     - param_0: 0.0
     - param_1: 100.0
     - param_2: 100.0
   ```

2. **手动映射参数**：
   - 在VST插件界面中调节参数
   - 观察控制台中哪个参数值发生变化
   - 记录正确的参数名称

### 滤镜链优化

**推荐的滤镜顺序**：
```
音频源
  ↓
Graillon音调 (第1个)
  ↓
TSE808失真 (第2个)
  ↓
TAL混响 (第3个)
  ↓
输出
```

**原因**：
- 音调处理在最前面，保证音质
- 失真在中间，增加色彩
- 混响在最后，增加空间感

## 📊 性能优化

### CPU使用率监控
- 这3个VST插件都是高质量插件
- 建议监控CPU使用率
- 必要时调整音频缓冲区大小

### 延迟优化
- VST插件可能引入音频延迟
- 在OBS设置中调整音频延迟补偿
- 使用ASIO驱动可以减少延迟

## 🎯 实际使用建议

### 直播场景
```
保守设置：
- Graillon: -2到+2半音
- TSE808: 15-40%失真
- 变化间隔: 5-8秒
```

### 录制场景
```
创意设置：
- Graillon: -4到+4半音
- TSE808: 10-60%失真
- 变化间隔: 2-4秒
```

### 音乐制作
```
专业设置：
- 精确的参数控制
- 更长的变化间隔
- 配合混响使用
```

现在您可以在OBS中正确设置VST 2.x滤镜，并通过程序实现自动化控制了！🎉
