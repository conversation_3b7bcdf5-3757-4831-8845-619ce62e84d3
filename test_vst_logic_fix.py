# -*- coding: utf-8 -*-
"""
测试VST逻辑修复
验证滤镜检测和参数检测逻辑是否正确
"""

def test_filter_detection_logic():
    """测试滤镜检测逻辑"""
    print("🔍 测试滤镜检测逻辑修复")
    print("=" * 50)
    
    # 模拟从OBS获取的滤镜列表（基于您的实际日志）
    mock_response = {
        'filters': [
            {
                'filterName': 'Graillon音调',
                'filterKind': 'vst_filter'
            },
            {
                'filterName': 'TSE808失真', 
                'filterKind': 'vst_filter'
            },
            {
                'filterName': 'TAL混响',
                'filterKind': 'vst_filter'
            }
        ]
    }
    
    # 要查找的滤镜
    target_filters = ['Graillon音调', 'TSE808失真', 'TAL混响', '不存在的滤镜']
    
    for target_filter in target_filters:
        print(f"\n🔍 查找滤镜: '{target_filter}'")
        
        found = False
        filters = mock_response.get('filters', [])
        
        for filter_info in filters:
            filter_name_found = filter_info.get('filterName', '')
            filter_kind = filter_info.get('filterKind', '')
            
            # 使用修复后的逻辑
            if filter_name_found.strip() == target_filter.strip():
                if filter_kind == 'vst_filter':
                    print(f"  ✅ 找到VST 2.x滤镜: '{target_filter}'")
                    found = True
                    break
                else:
                    print(f"  ⚠️ 滤镜存在但不是VST类型: '{target_filter}'")
                    found = True
                    break
        
        if not found:
            print(f"  ❌ 未找到滤镜: '{target_filter}'")

def test_parameter_detection_logic():
    """测试参数检测逻辑"""
    print("\n🔍 测试参数检测逻辑修复")
    print("=" * 50)
    
    # 模拟从OBS获取的VST滤镜设置（基于您的实际日志）
    mock_settings = {
        'pitch': 2.4844454727986847,
        'formant': 100.0,
        'mix': 100.0,
        'plugin_path': 'C:\\Program Files\\VSTPlugins\\Auburn Sounds Graillon 3-64.dll'
    }
    
    # 要查找的参数
    target_params = ['pitch', 'formant', 'mix', 'drive', 'tone', '不存在的参数']
    
    for target_param in target_params:
        print(f"\n🔍 查找参数: '{target_param}'")
        
        # 使用修复后的逻辑
        if target_param in mock_settings:
            value = mock_settings[target_param]
            print(f"  ✅ 直接找到参数 '{target_param}': {value}")
            try:
                float_value = float(value)
                print(f"  ✅ 参数值可转换为数字: {float_value}")
            except (ValueError, TypeError):
                print(f"  ⚠️ 参数值无法转换为数字: {value}")
        else:
            print(f"  ❌ 未找到参数: '{target_param}'")

def test_parameter_setting_logic():
    """测试参数设置逻辑"""
    print("\n🔧 测试参数设置逻辑修复")
    print("=" * 50)
    
    # 模拟当前滤镜设置
    current_settings = {
        'pitch': 0.0,
        'formant': 100.0,
        'mix': 100.0
    }
    
    # 要设置的参数
    test_cases = [
        ('pitch', 5.5),      # 存在的参数
        ('formant', 95.0),   # 存在的参数
        ('drive', 30.0),     # 不存在的参数
    ]
    
    for property_name, new_value in test_cases:
        print(f"\n🔧 设置参数: '{property_name}' = {new_value}")
        
        # 使用修复后的逻辑
        if property_name in current_settings:
            old_value = current_settings[property_name]
            print(f"  ✅ 参数 '{property_name}' 已存在，当前值: {old_value}")
        else:
            print(f"  ℹ️ 参数 '{property_name}' 不存在，将创建新参数")
            old_value = "新参数"
        
        # 设置新值
        current_settings[property_name] = new_value
        print(f"  ✅ 参数已更新: '{property_name}' {old_value} → {new_value}")

def test_string_comparison():
    """测试字符串比较问题"""
    print("\n🔤 测试字符串比较问题")
    print("=" * 50)
    
    # 可能的字符串比较问题
    test_cases = [
        ('Graillon音调', 'Graillon音调'),      # 完全相同
        ('Graillon音调 ', 'Graillon音调'),     # 末尾空格
        (' Graillon音调', 'Graillon音调'),     # 开头空格
        ('Graillon音调\n', 'Graillon音调'),    # 换行符
        ('Graillon音调\t', 'Graillon音调'),    # 制表符
    ]
    
    for str1, str2 in test_cases:
        print(f"\n比较: '{repr(str1)}' vs '{repr(str2)}'")
        print(f"  直接比较: {str1 == str2}")
        print(f"  strip()后比较: {str1.strip() == str2.strip()}")
        print(f"  长度: {len(str1)} vs {len(str2)}")

def main():
    print("🔧 VST逻辑修复测试")
    print("=" * 60)
    
    test_filter_detection_logic()
    test_parameter_detection_logic()
    test_parameter_setting_logic()
    test_string_comparison()
    
    print("\n" + "=" * 60)
    print("📋 修复总结:")
    print("1. ✅ 修复了滤镜检测中的字符串比较问题")
    print("2. ✅ 改进了参数检测的日志输出")
    print("3. ✅ 优化了参数设置的逻辑")
    print("4. ✅ 添加了调试信息帮助排查问题")
    print("\n🎯 现在VST控制应该能正确检测滤镜和参数了！")

if __name__ == "__main__":
    main()
