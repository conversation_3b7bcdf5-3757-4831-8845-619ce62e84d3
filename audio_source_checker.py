# -*- coding: utf-8 -*-
"""
音频源检测工具
帮助检测和建议合适的音频源用于VST控制
"""

import sys
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QTextEdit, QComboBox, QGroupBox, QFormLayout
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class AudioSourceChecker(QMainWindow):
    """音频源检测工具"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        
    def setup_ui(self):
        """设置界面"""
        self.setWindowTitle("🎤 音频源检测工具")
        self.setGeometry(100, 100, 700, 600)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title = QLabel("🎤 音频源检测和建议工具")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 问题分析
        problem_group = QGroupBox("🔍 问题分析")
        problem_layout = QVBoxLayout(problem_group)
        
        problem_text = """
根据您的日志分析，VST参数确实在变化，但您听不到效果的可能原因：

1. 🎬 当前音频源问题：
   - 您选择的是视频文件：'汤圆2.14 14点54.mp4'
   - 视频文件的音频可能不适合实时VST处理
   - 建议使用实时音频源（如麦克风、音频输入）

2. 🎛️ VST滤镜应用问题：
   - VST滤镜可能没有正确应用到音频流
   - 需要确认滤镜在OBS中是否启用
   - 检查滤镜顺序是否正确

3. 📊 参数范围问题：
   - 音调变化-11.1半音可能过于极端
   - 建议使用更温和的参数范围
        """
        
        problem_label = QLabel(problem_text)
        problem_label.setWordWrap(True)
        problem_label.setStyleSheet("padding: 10px; background: #f8f9fa; border-radius: 6px;")
        problem_layout.addWidget(problem_label)
        
        layout.addWidget(problem_group)
        
        # 建议的音频源
        source_group = QGroupBox("🎯 建议的音频源类型")
        source_layout = QFormLayout(source_group)
        
        # 推荐音频源列表
        recommended_sources = [
            ("🎤 麦克风", "最佳选择 - 实时音频输入，VST效果最明显"),
            ("🔊 桌面音频", "系统音频 - 可以处理播放的音乐"),
            ("📻 音频输入捕获", "外部音频设备 - 专业音频接口"),
            ("🎵 音频输出捕获", "捕获特定应用音频"),
            ("🎹 虚拟音频设备", "如VoiceMeeter等虚拟音频线路")
        ]
        
        for source_name, description in recommended_sources:
            source_layout.addRow(source_name, QLabel(description))
        
        layout.addWidget(source_group)
        
        # 不推荐的音频源
        not_recommended_group = QGroupBox("❌ 不推荐的音频源")
        not_recommended_layout = QFormLayout(not_recommended_group)
        
        not_recommended_sources = [
            ("🎬 视频文件", "预录制内容，VST效果不明显"),
            ("📁 媒体文件", "静态文件，无法体现实时效果"),
            ("🖥️ 显示器捕获", "主要是视频，音频处理效果有限")
        ]
        
        for source_name, description in not_recommended_sources:
            not_recommended_layout.addRow(source_name, QLabel(description))
        
        layout.addWidget(not_recommended_group)
        
        # 测试建议
        test_group = QGroupBox("🧪 测试建议")
        test_layout = QVBoxLayout(test_group)
        
        test_text = """
为了验证VST控制功能是否正常工作，建议按以下步骤测试：

1. 🎤 使用麦克风测试：
   - 在OBS中添加"音频输入捕获"源
   - 选择您的麦克风设备
   - 在程序中选择这个音频源
   - 启用VST音调控制，说话测试

2. 🔊 使用桌面音频测试：
   - 确保OBS中有"桌面音频"源
   - 播放音乐或视频
   - 在程序中选择桌面音频源
   - 启用VST控制，观察音频变化

3. 📊 调整参数范围：
   - 音调控制：建议 -3 到 +3 半音
   - 失真控制：建议 20-50% 强度
   - 混响控制：建议 15-35% 混响量

4. 🎛️ 检查OBS滤镜：
   - 在OBS中查看音频源的滤镜列表
   - 确认VST滤镜已添加且启用
   - 手动调节VST插件参数验证效果
        """
        
        test_label = QLabel(test_text)
        test_label.setWordWrap(True)
        test_label.setStyleSheet("padding: 10px; background: #e8f5e8; border-radius: 6px;")
        test_layout.addWidget(test_label)
        
        layout.addWidget(test_group)
        
        # 快速测试按钮
        button_layout = QHBoxLayout()
        
        self.mic_test_btn = QPushButton("🎤 麦克风测试指南")
        self.mic_test_btn.clicked.connect(self.show_mic_test_guide)
        button_layout.addWidget(self.mic_test_btn)
        
        self.desktop_test_btn = QPushButton("🔊 桌面音频测试指南")
        self.desktop_test_btn.clicked.connect(self.show_desktop_test_guide)
        button_layout.addWidget(self.desktop_test_btn)
        
        self.vst_check_btn = QPushButton("🎛️ VST滤镜检查指南")
        self.vst_check_btn.clicked.connect(self.show_vst_check_guide)
        button_layout.addWidget(self.vst_check_btn)
        
        layout.addLayout(button_layout)
        
        # 日志显示
        layout.addWidget(QLabel("📋 详细指南:"))
        self.guide_text = QTextEdit()
        self.guide_text.setReadOnly(True)
        layout.addWidget(self.guide_text)
        
        self.log("🚀 音频源检测工具已启动")
        self.log("💡 根据您的情况，建议切换到实时音频源进行测试")
        
    def show_mic_test_guide(self):
        """显示麦克风测试指南"""
        self.guide_text.clear()
        guide = """
🎤 麦克风测试完整指南

1. 在OBS中设置：
   ✅ 添加"音频输入捕获"源
   ✅ 选择您的麦克风设备
   ✅ 确认音频电平有反应
   ✅ 测试音频是否正常

2. 在程序中设置：
   ✅ 选择刚创建的音频输入源
   ✅ 启用VST音调控制
   ✅ 设置温和的参数范围：-2到+2半音
   ✅ 设置较短的变化间隔：3秒

3. 测试步骤：
   ✅ 对着麦克风说话
   ✅ 观察控制台日志中的参数变化
   ✅ 听音调是否有变化
   ✅ 如果没有效果，检查OBS中的滤镜设置

4. 故障排除：
   ✅ 确认麦克风在OBS中有音频信号
   ✅ 检查VST滤镜是否启用
   ✅ 尝试手动调节VST插件参数
   ✅ 检查音频监听设置
        """
        self.guide_text.setText(guide)
        self.log("📖 显示麦克风测试指南")
        
    def show_desktop_test_guide(self):
        """显示桌面音频测试指南"""
        self.guide_text.clear()
        guide = """
🔊 桌面音频测试完整指南

1. 在OBS中设置：
   ✅ 确认"桌面音频"源存在且启用
   ✅ 播放音乐或视频测试音频
   ✅ 观察音频电平是否有反应
   ✅ 调整音频音量到合适水平

2. 在程序中设置：
   ✅ 选择"桌面音频"源
   ✅ 启用VST失真控制（对音乐效果更明显）
   ✅ 设置参数：Drive 30-60%, Tone 40-70%
   ✅ 设置变化间隔：4秒

3. 测试步骤：
   ✅ 播放有节奏的音乐
   ✅ 启用VST控制功能
   ✅ 听音乐的失真效果变化
   ✅ 观察参数变化日志

4. 优化建议：
   ✅ 选择动态范围大的音乐
   ✅ 避免过于安静的音频
   ✅ 可以同时启用多个VST效果
   ✅ 调整OBS音频监听为"监听和输出"
        """
        self.guide_text.setText(guide)
        self.log("📖 显示桌面音频测试指南")
        
    def show_vst_check_guide(self):
        """显示VST滤镜检查指南"""
        self.guide_text.clear()
        guide = """
🎛️ VST滤镜检查完整指南

1. 检查滤镜是否创建：
   ✅ 在OBS中右键音频源 → 滤镜
   ✅ 查看是否有"Graillon音调"、"TSE808失真"、"TAL混响"
   ✅ 确认滤镜类型为"VST 2.x 插件"
   ✅ 检查滤镜是否启用（眼睛图标）

2. 检查插件加载：
   ✅ 双击VST滤镜打开插件界面
   ✅ 确认插件正确加载（不是错误界面）
   ✅ 手动调节插件参数测试效果
   ✅ 关闭插件界面

3. 检查滤镜顺序：
   ✅ 推荐顺序：Graillon → TSE808 → TAL混响
   ✅ 拖拽调整滤镜顺序
   ✅ 音调处理在最前面
   ✅ 混响处理在最后面

4. 检查音频路由：
   ✅ 确认音频源有信号输入
   ✅ 检查OBS音频监听设置
   ✅ 测试音频输出是否正常
   ✅ 验证滤镜是否真正处理音频

5. 参数验证：
   ✅ 在程序中查看参数变化日志
   ✅ 在OBS中观察VST插件参数是否同步变化
   ✅ 对比手动调节和自动控制的效果
   ✅ 确认参数范围是否合理
        """
        self.guide_text.setText(guide)
        self.log("📖 显示VST滤镜检查指南")
        
    def log(self, message):
        """记录日志"""
        print(message)

def main():
    app = QApplication(sys.argv)
    window = AudioSourceChecker()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
