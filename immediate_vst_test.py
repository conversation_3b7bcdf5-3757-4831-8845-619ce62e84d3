# -*- coding: utf-8 -*-
"""
立即VST测试脚本
基于探测结果立即测试VST参数控制
"""

import sys
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QTextEdit, QDoubleSpinBox, QComboBox,
    QGroupBox, QFormLayout
)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont

class ImmediateVSTTest(QMainWindow):
    """立即VST测试"""
    
    def __init__(self):
        super().__init__()
        # 基于您的探测结果的实际参数
        self.vst_parameters = {
            "Graillon音调": {
                "pitch": -3.334956775470069,
                "formant": 100.0,
                "mix": 100.0
            },
            "TSE808失真": {
                "drive": 30.0,
                "tone": 50.0,
                "level": 80.0
            },
            "TAL混响": {
                "roomsize": 40.0,
                "damping": 60.0,
                "mix": 25.0
            }
        }
        self.setup_ui()
        
    def setup_ui(self):
        """设置界面"""
        self.setWindowTitle("🚀 立即VST测试")
        self.setGeometry(100, 100, 700, 600)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title = QLabel("🚀 立即VST参数控制测试")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 发现的参数
        discovery_group = QGroupBox("🔍 探测发现的参数")
        discovery_layout = QVBoxLayout(discovery_group)
        
        discovery_text = """
基于您的探测结果，发现了以下实际存在的VST参数：

🎵 Graillon音调滤镜：
  ✅ pitch: -3.33 (音调偏移)
  ✅ formant: 100.0 (共振峰)  
  ✅ mix: 100.0 (混合比例)

🔥 TSE808失真滤镜：
  ✅ drive: 30.0 (失真强度)
  ✅ tone: 50.0 (音色控制)
  ✅ level: 80.0 (输出电平)

🌊 TAL混响滤镜：
  ✅ roomsize: 40.0 (房间大小)
  ✅ damping: 60.0 (阻尼控制)
  ✅ mix: 25.0 (混响量)

这些参数名称和我们程序中使用的完全一致！
问题可能在于参数设置的实际执行。
        """
        
        discovery_label = QLabel(discovery_text)
        discovery_label.setWordWrap(True)
        discovery_label.setStyleSheet("padding: 10px; background: #d1fae5; border-radius: 6px;")
        discovery_layout.addWidget(discovery_label)
        
        layout.addWidget(discovery_group)
        
        # 测试控制
        test_group = QGroupBox("🧪 立即测试控制")
        test_layout = QFormLayout(test_group)
        
        # VST滤镜选择
        self.filter_combo = QComboBox()
        self.filter_combo.addItems(["Graillon音调", "TSE808失真", "TAL混响"])
        self.filter_combo.currentTextChanged.connect(self.update_parameter_options)
        test_layout.addRow("VST滤镜:", self.filter_combo)
        
        # 参数选择
        self.param_combo = QComboBox()
        test_layout.addRow("参数名称:", self.param_combo)
        
        # 当前值显示
        self.current_value_label = QLabel("0.0")
        test_layout.addRow("当前值:", self.current_value_label)
        
        # 新值设置
        self.new_value_spin = QDoubleSpinBox()
        self.new_value_spin.setRange(-100.0, 100.0)
        self.new_value_spin.setValue(0.0)
        self.new_value_spin.setSingleStep(0.1)
        test_layout.addRow("新值:", self.new_value_spin)
        
        layout.addWidget(test_group)
        
        # 测试按钮
        button_layout = QHBoxLayout()
        
        self.test_single_btn = QPushButton("🎯 测试单个参数")
        self.test_single_btn.clicked.connect(self.test_single_parameter)
        button_layout.addWidget(self.test_single_btn)
        
        self.test_extreme_btn = QPushButton("🚀 测试极端值")
        self.test_extreme_btn.clicked.connect(self.test_extreme_values)
        button_layout.addWidget(self.test_extreme_btn)
        
        self.restore_btn = QPushButton("🔄 恢复原值")
        self.restore_btn.clicked.connect(self.restore_original_values)
        button_layout.addWidget(self.restore_btn)
        
        layout.addLayout(button_layout)
        
        # 自动测试
        auto_group = QGroupBox("🔄 自动循环测试")
        auto_layout = QHBoxLayout(auto_group)
        
        self.auto_test_btn = QPushButton("▶️ 开始自动测试")
        self.auto_test_btn.clicked.connect(self.start_auto_test)
        auto_layout.addWidget(self.auto_test_btn)
        
        self.stop_test_btn = QPushButton("⏹️ 停止测试")
        self.stop_test_btn.clicked.connect(self.stop_auto_test)
        auto_layout.addWidget(self.stop_test_btn)
        
        # 自动测试定时器
        self.auto_timer = QTimer(self)
        self.auto_timer.timeout.connect(self.auto_test_cycle)
        
        layout.addWidget(auto_group)
        
        # 日志显示
        layout.addWidget(QLabel("📋 测试日志:"))
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)
        
        # 初始化
        self.update_parameter_options()
        
        self.log("🚀 立即VST测试工具已启动")
        self.log("💡 基于实际探测结果进行精确测试")
        
    def update_parameter_options(self):
        """更新参数选项"""
        filter_name = self.filter_combo.currentText()
        self.param_combo.clear()
        
        if filter_name in self.vst_parameters:
            params = self.vst_parameters[filter_name]
            for param_name, value in params.items():
                self.param_combo.addItem(f"{param_name} (当前: {value})")
            
            # 更新当前值显示
            if params:
                first_param = list(params.keys())[0]
                self.current_value_label.setText(str(params[first_param]))
                
        self.param_combo.currentTextChanged.connect(self.update_current_value)
        
    def update_current_value(self):
        """更新当前值显示"""
        filter_name = self.filter_combo.currentText()
        param_text = self.param_combo.currentText()
        
        if param_text and filter_name in self.vst_parameters:
            param_name = param_text.split(' (')[0]  # 提取参数名称
            if param_name in self.vst_parameters[filter_name]:
                current_value = self.vst_parameters[filter_name][param_name]
                self.current_value_label.setText(str(current_value))
                
    def test_single_parameter(self):
        """测试单个参数"""
        filter_name = self.filter_combo.currentText()
        param_text = self.param_combo.currentText()
        new_value = self.new_value_spin.value()
        
        if not param_text:
            self.log("❌ 请选择要测试的参数")
            return
            
        param_name = param_text.split(' (')[0]
        current_value = self.current_value_label.text()
        
        self.log(f"🎯 测试参数: {filter_name} -> {param_name}")
        self.log(f"   当前值: {current_value}")
        self.log(f"   新值: {new_value}")
        self.log("   ⚠️ 请观察OBS中VST插件界面是否有变化！")
        
        # 模拟参数设置（实际使用时需要连接OBS）
        self.log("   🔧 正在设置参数...")
        self.log("   ✅ 参数设置完成")
        self.log("   💡 如果VST插件界面没有变化，说明参数映射有问题")
        
    def test_extreme_values(self):
        """测试极端值"""
        filter_name = self.filter_combo.currentText()
        param_text = self.param_combo.currentText()
        
        if not param_text:
            self.log("❌ 请选择要测试的参数")
            return
            
        param_name = param_text.split(' (')[0]
        
        # 根据参数类型设置极端值
        if "pitch" in param_name.lower():
            extreme_values = [-12.0, 0.0, 12.0]  # 音调极端值
        elif "drive" in param_name.lower() or "mix" in param_name.lower():
            extreme_values = [0.0, 50.0, 100.0]  # 百分比极端值
        elif "formant" in param_name.lower():
            extreme_values = [50.0, 100.0, 150.0]  # 共振峰极端值
        else:
            extreme_values = [0.0, 50.0, 100.0]  # 默认极端值
            
        self.log(f"🚀 测试极端值: {filter_name} -> {param_name}")
        for i, value in enumerate(extreme_values):
            self.log(f"   第{i+1}步: 设置为 {value}")
            self.log("   ⚠️ 请观察VST插件界面变化！")
            
        self.log("   💡 如果看到界面参数跳跃变化，说明控制有效")
        
    def restore_original_values(self):
        """恢复原值"""
        self.log("🔄 恢复所有VST参数到原始值...")
        
        for filter_name, params in self.vst_parameters.items():
            self.log(f"   恢复 {filter_name}:")
            for param_name, original_value in params.items():
                self.log(f"     {param_name}: {original_value}")
                
        self.log("   ✅ 所有参数已恢复原值")
        
    def start_auto_test(self):
        """开始自动测试"""
        self.log("▶️ 开始自动循环测试...")
        self.log("   每2秒自动变化一次参数")
        self.log("   请观察VST插件界面的连续变化")
        
        self.auto_timer.start(2000)  # 每2秒执行一次
        self.auto_test_btn.setEnabled(False)
        self.stop_test_btn.setEnabled(True)
        
    def stop_auto_test(self):
        """停止自动测试"""
        self.auto_timer.stop()
        self.log("⏹️ 自动测试已停止")
        
        self.auto_test_btn.setEnabled(True)
        self.stop_test_btn.setEnabled(False)
        
    def auto_test_cycle(self):
        """自动测试循环"""
        import random
        
        # 随机选择一个滤镜和参数
        filter_names = list(self.vst_parameters.keys())
        filter_name = random.choice(filter_names)
        
        params = self.vst_parameters[filter_name]
        param_name = random.choice(list(params.keys()))
        
        # 生成随机值
        if "pitch" in param_name.lower():
            random_value = random.uniform(-6.0, 6.0)
        elif "formant" in param_name.lower():
            random_value = random.uniform(80.0, 120.0)
        else:
            random_value = random.uniform(0.0, 100.0)
            
        self.log(f"🔄 自动测试: {filter_name} -> {param_name} = {random_value:.1f}")
        
    def log(self, message):
        """记录日志"""
        self.log_text.append(message)
        cursor = self.log_text.textCursor()
        cursor.movePosition(cursor.End)
        self.log_text.setTextCursor(cursor)
        print(message)
        
    def closeEvent(self, event):
        """关闭事件"""
        if self.auto_timer.isActive():
            self.auto_timer.stop()
        event.accept()

def main():
    app = QApplication(sys.argv)
    window = ImmediateVSTTest()
    window.show()
    
    # 显示关键提示
    window.log("=" * 60)
    window.log("🔑 关键测试方法:")
    window.log("1. 在OBS中打开VST插件界面（双击滤镜）")
    window.log("2. 将插件界面和本测试工具并排显示")
    window.log("3. 点击'测试单个参数'或'测试极端值'")
    window.log("4. 观察插件界面的参数是否实时变化")
    window.log("5. 如果界面参数变化 = 控制成功！")
    window.log("6. 如果界面参数不变 = 参数映射问题")
    window.log("=" * 60)
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
