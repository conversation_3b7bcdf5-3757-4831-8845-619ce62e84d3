#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单音频设备测试工具
快速验证音频输出到指定设备是否工作
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QLabel, QPushButton, QComboBox, QTextEdit
from PyQt5.QtCore import Qt
from PyQt5.QtMultimedia import QAudioDeviceInfo, QAudio, QAudioOutput, QAudioFormat
from PyQt5.QtCore import QIODevice
import numpy as np

try:
    import soundfile as sf
    AUDIO_PROCESSING_AVAILABLE = True
except ImportError:
    AUDIO_PROCESSING_AVAILABLE = False
    print("⚠️ soundfile库未安装，将使用numpy生成测试音频")

class SimpleAudioTest(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🔧 简单音频设备测试")
        self.setGeometry(200, 200, 600, 400)
        
        self.selected_audio_device = None
        self.current_audio_output = None
        self.audio_buffers = []
        
        self.init_ui()
        self.load_audio_devices()
        
    def init_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title = QLabel("🎯 音频设备输出测试")
        title.setStyleSheet("""
            QLabel {
                font-size: 16pt;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 设备选择
        device_layout = QHBoxLayout()
        device_layout.addWidget(QLabel("选择音频设备:"))
        
        self.device_combo = QComboBox()
        self.device_combo.setMinimumWidth(300)
        self.device_combo.currentTextChanged.connect(self.on_device_changed)
        device_layout.addWidget(self.device_combo)
        
        device_layout.addStretch()
        layout.addLayout(device_layout)
        
        # 测试按钮
        test_layout = QHBoxLayout()
        
        self.test_btn = QPushButton("🎵 播放测试音调")
        self.test_btn.clicked.connect(self.test_audio_output)
        self.test_btn.setStyleSheet("""
            QPushButton {
                background: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12pt;
            }
            QPushButton:hover {
                background: #2980b9;
            }
        """)
        test_layout.addWidget(self.test_btn)
        
        test_layout.addStretch()
        layout.addLayout(test_layout)
        
        # 日志显示
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        self.log_text.setStyleSheet("""
            QTextEdit {
                background: #2c3e50;
                color: #ecf0f1;
                border: 1px solid #34495e;
                border-radius: 5px;
                padding: 10px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 10pt;
            }
        """)
        layout.addWidget(self.log_text)
        
    def load_audio_devices(self):
        """加载音频输出设备"""
        try:
            devices = QAudioDeviceInfo.availableDevices(QAudio.AudioOutput)
            
            self.device_combo.clear()
            self.device_combo.addItem("系统默认设备", None)
            
            for device in devices:
                device_name = device.deviceName()
                self.device_combo.addItem(f"🎵 {device_name}", device)
                self.log(f"发现音频设备: {device_name}")
            
            self.log(f"总共找到 {len(devices)} 个音频输出设备")
            
        except Exception as e:
            self.log(f"❌ 加载音频设备时出错: {e}")
            
    def on_device_changed(self, device_name):
        """设备选择改变"""
        selected_device = self.device_combo.currentData()
        
        if selected_device:
            self.selected_audio_device = selected_device
            self.log(f"✅ 已选择设备: {device_name}")
        else:
            self.selected_audio_device = None
            self.log(f"ℹ️ 已选择系统默认设备")
    
    def test_audio_output(self):
        """测试音频输出"""
        try:
            if not self.selected_audio_device:
                self.log("⚠️ 请先选择一个音频设备")
                return
                
            self.log(f"🧪 开始测试设备: {self.selected_audio_device.deviceName()}")
            
            # 生成440Hz正弦波测试音调
            duration = 2.0  # 2秒
            sample_rate = 44100
            frequency = 440.0
            
            t = np.linspace(0, duration, int(sample_rate * duration), False)
            audio_data = 0.3 * np.sin(2 * np.pi * frequency * t)
            
            # 转为立体声
            if len(audio_data.shape) == 1:
                audio_data = np.column_stack((audio_data, audio_data))
            
            # 播放音频
            success = self.play_audio_through_device(audio_data, sample_rate)
            
            if success:
                self.log("✅ 测试音调播放成功！")
            else:
                self.log("❌ 测试音调播放失败")
                
        except Exception as e:
            self.log(f"❌ 测试时出错: {e}")
    
    def play_audio_through_device(self, audio_data, sample_rate):
        """通过指定设备播放音频"""
        try:
            self.log(f"🎯 准备播放音频数据...")
            self.log(f"  - 数据形状: {audio_data.shape}")
            self.log(f"  - 采样率: {sample_rate}")
            
            # 转换为16位整数格式
            audio_data_int16 = (audio_data * 32767).astype(np.int16)
            
            # 创建音频格式
            format = QAudioFormat()
            format.setSampleRate(int(sample_rate))
            format.setChannelCount(audio_data_int16.shape[1])
            format.setSampleSize(16)
            format.setCodec("audio/pcm")
            format.setByteOrder(QAudioFormat.LittleEndian)
            format.setSampleType(QAudioFormat.SignedInt)
            
            self.log(f"  - 音频格式: {format.sampleRate()}Hz, {format.channelCount()}声道")
            
            # 检查设备支持
            if not self.selected_audio_device.isFormatSupported(format):
                self.log("⚠️ 设备不支持指定格式，使用最接近格式")
                format = self.selected_audio_device.nearestFormat(format)
                self.log(f"  - 调整后格式: {format.sampleRate()}Hz, {format.channelCount()}声道")
            
            # 停止当前播放
            if self.current_audio_output:
                try:
                    self.current_audio_output.stop()
                except:
                    pass
            
            # 创建音频输出
            self.current_audio_output = QAudioOutput(self.selected_audio_device, format)
            self.current_audio_output.setVolume(0.5)
            self.log("  - 音频输出对象创建成功")
            
            # 创建音频数据
            audio_bytes = audio_data_int16.tobytes()
            self.log(f"  - 音频数据大小: {len(audio_bytes)} 字节")
            
            # 开始播放
            io_device = self.current_audio_output.start()
            if io_device:
                bytes_written = io_device.write(audio_bytes)
                self.log(f"  - 写入数据: {bytes_written} 字节")
                
                if bytes_written > 0:
                    self.log(f"✅ 成功播放到设备: {self.selected_audio_device.deviceName()}")
                    return True
                else:
                    self.log("❌ 写入数据失败")
                    return False
            else:
                self.log("❌ 无法启动音频输出")
                return False
                
        except Exception as e:
            self.log(f"❌ 播放失败: {e}")
            import traceback
            self.log(f"详细错误: {traceback.format_exc()}")
            return False
    
    def log(self, message):
        """添加日志消息"""
        self.log_text.append(message)
        self.log_text.ensureCursorVisible()
        print(message)  # 同时输出到控制台

def main():
    app = QApplication(sys.argv)
    
    window = SimpleAudioTest()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
