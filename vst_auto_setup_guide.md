# 🎛️ VST自动化设置指南

## 📋 功能概述

现在你的OBS去重软件已经支持**完全自动化的VST插件控制**！程序可以：

1. **自动检测并添加VST 2.x滤镜**到你的音频源
2. **自动应用你的三个插件**：
   - Auburn Sounds Graillon 3-64 (音调控制)
   - TSE_808_2.0_x64 (失真控制)  
   - TAL-Reverb-4-64 (混响控制，预留)
3. **自动调节参数**实现音频去重效果

## 🔧 使用步骤

### 步骤1：确保VST插件已安装

确认你的VST插件位于：
```
C:\Program Files\VSTPlugins\
├── Auburn Sounds Graillon 3-64.dll
├── TSE_808_2.0_x64.dll
└── TAL-Reverb-4-64.dll
```

### 步骤2：在OBS中准备音频源

1. 确保你有一个音频源（如麦克风、音频输入等）
2. 不需要手动添加VST滤镜 - 程序会自动处理！

### 步骤3：在程序中启用VST功能

#### 方法1：单独启用
1. 连接到OBS
2. 在"音频去重"标签页中选择音频源
3. 切换到"🎵 VST音调"或"🔥 VST失真"子标签页
4. 勾选"启用VST音调控制"或"启用VST失真控制"
5. 程序会自动：
   - 检测滤镜是否存在
   - 如果不存在，自动创建VST滤镜
   - 开始随机调节参数

#### 方法2：一键启动
1. 连接到OBS并选择音频源
2. 点击"一键启动"按钮
3. 在弹出的对话框中勾选：
   - "🎵 VST音调控制"
   - "🔥 VST失真控制"
4. 点击"启动选中功能"

## ⚙️ 参数设置

### VST音调控制（Graillon）
- **滤镜名称**：Graillon音调（可自定义）
- **音调范围**：-6.0 到 +6.0 半音
- **变化间隔**：3.0 秒
- **自动参数**：
  - pitch: 音调偏移（-6到+6半音）
  - formant: 共振峰（保持100%自然）
  - mix: 混合量（100%全湿信号）

### VST失真控制（TSE808）
- **滤镜名称**：TSE808失真（可自定义）
- **失真强度**：10.0% 到 70.0%
- **音色范围**：20.0% 到 80.0%
- **变化间隔**：3.0 秒
- **自动参数**：
  - drive: 失真强度（10-70%）
  - tone: 音色调节（20-80%）
  - level: 输出电平（固定80%）

## 🎯 音频去重效果

### 音调控制效果
- **轻微变调**：-3到+3半音，适合语音直播
- **明显变声**：-6到+6半音，适合娱乐内容
- **实时变化**：每3秒自动调整，避免固定音调

### 失真控制效果
- **温暖失真**：10-30%强度，增加音频温度
- **明显效果**：40-70%强度，显著改变音频特征
- **音色调节**：20-80%范围，改变音频频响特性

## 🔍 故障排除

### 问题1：提示"VST滤镜不存在"
**解决方案**：
- 检查VST插件路径是否正确
- 确认插件文件存在且可访问
- 重启OBS和程序

### 问题2：参数调节无效果
**解决方案**：
- 在OBS中手动打开VST插件界面
- 观察参数是否实际发生变化
- 检查控制台输出的调试信息

### 问题3：音频质量下降
**解决方案**：
- 减小参数变化范围
- 增加变化间隔时间
- 调整VST插件内部质量设置

## 📊 推荐设置组合

### 轻度去重（适合日常直播）
```
VST音调：-2到+2半音，间隔5秒
VST失真：15-35%强度，间隔4秒
```

### 中度去重（适合内容创作）
```
VST音调：-4到+4半音，间隔3秒
VST失真：20-50%强度，间隔3秒
```

### 重度去重（适合特殊需求）
```
VST音调：-6到+6半音，间隔2秒
VST失真：30-70%强度，间隔2秒
```

## 🎛️ 高级功能

### 自动滤镜创建
程序会自动：
1. 检测音频源是否已有对应VST滤镜
2. 如果不存在，自动创建VST 2.x滤镜
3. 设置默认参数值
4. 开始自动调节

### 智能参数匹配
程序支持：
- 直接参数名匹配（如"pitch"）
- 模糊参数名匹配（如"Pitch"、"PITCH"）
- 参数值范围自动适配

### 实时调试信息
程序会输出详细日志：
- VST滤镜检测结果
- 参数设置过程
- 错误诊断信息

## 🚀 未来扩展

程序已预留TAL混响控制功能，可以轻松扩展：
- 房间大小自动调节
- 混响量动态变化
- 阻尼参数随机调整

---

**🎉 现在你可以实现完全自动化的VST音频去重了！**

程序会在OBS中自动添加VST滤镜，并按照你设置的范围和时间间隔自动调节参数，实现音频去重效果。无需手动操作，一键启动即可！
