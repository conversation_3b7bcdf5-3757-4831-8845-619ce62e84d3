# -*- coding: utf-8 -*-
"""
VST自动设置工具
自动为OBS音频源添加VST滤镜
"""

import sys
import os
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QLineEdit, QTextEdit, QComboBox,
    QGroupBox, QFormLayout, QFileDialog, QMessageBox, QCheckBox
)
from PyQt5.QtCore import Qt, QSettings
from PyQt5.QtGui import QFont

class VSTAutoSetupTool(QMainWindow):
    """VST自动设置工具"""
    
    def __init__(self):
        super().__init__()
        self.settings = QSettings("OBS去重工具", "VSTAutoSetup")
        self.setup_ui()
        self.load_settings()
        
    def setup_ui(self):
        """设置界面"""
        self.setWindowTitle("🔧 VST滤镜自动设置工具")
        self.setGeometry(100, 100, 700, 600)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title = QLabel("🔧 VST滤镜自动设置工具")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 说明
        info = QLabel("此工具可以自动为OBS音频源添加VST 2.x滤镜，无需手动操作")
        info.setAlignment(Qt.AlignCenter)
        info.setStyleSheet("color: #666; margin: 10px;")
        layout.addWidget(info)
        
        # VST插件路径配置
        path_group = QGroupBox("🎛️ VST插件路径配置")
        path_layout = QFormLayout(path_group)
        
        # Auburn Sounds Graillon 3-64
        graillon_layout = QHBoxLayout()
        self.graillon_path_edit = QLineEdit()
        self.graillon_path_edit.setPlaceholderText("选择 Auburn Sounds Graillon 3-64.dll")
        graillon_browse_btn = QPushButton("浏览")
        graillon_browse_btn.clicked.connect(lambda: self.browse_vst_plugin(self.graillon_path_edit))
        graillon_layout.addWidget(self.graillon_path_edit)
        graillon_layout.addWidget(graillon_browse_btn)
        path_layout.addRow("🎵 Graillon 3-64:", graillon_layout)
        
        # TSE_808_2.0_x64
        tse808_layout = QHBoxLayout()
        self.tse808_path_edit = QLineEdit()
        self.tse808_path_edit.setPlaceholderText("选择 TSE_808_2.0_x64.dll")
        tse808_browse_btn = QPushButton("浏览")
        tse808_browse_btn.clicked.connect(lambda: self.browse_vst_plugin(self.tse808_path_edit))
        tse808_layout.addWidget(self.tse808_path_edit)
        tse808_layout.addWidget(tse808_browse_btn)
        path_layout.addRow("🔥 TSE 808:", tse808_layout)
        
        # TAL-Reverb-4-64
        tal_layout = QHBoxLayout()
        self.tal_path_edit = QLineEdit()
        self.tal_path_edit.setPlaceholderText("选择 TAL-Reverb-4-64.dll")
        tal_browse_btn = QPushButton("浏览")
        tal_browse_btn.clicked.connect(lambda: self.browse_vst_plugin(self.tal_path_edit))
        tal_layout.addWidget(self.tal_path_edit)
        tal_layout.addWidget(tal_browse_btn)
        path_layout.addRow("🌊 TAL Reverb:", tal_layout)
        
        # 自动检测按钮
        auto_detect_btn = QPushButton("🔍 自动检测插件路径")
        auto_detect_btn.clicked.connect(self.auto_detect_plugins)
        path_layout.addRow(auto_detect_btn)
        
        layout.addWidget(path_group)
        
        # 滤镜设置
        filter_group = QGroupBox("⚙️ 滤镜设置")
        filter_layout = QFormLayout(filter_group)
        
        self.graillon_name_edit = QLineEdit("Graillon音调")
        filter_layout.addRow("Graillon滤镜名称:", self.graillon_name_edit)
        
        self.tse808_name_edit = QLineEdit("TSE808失真")
        filter_layout.addRow("TSE808滤镜名称:", self.tse808_name_edit)
        
        self.tal_name_edit = QLineEdit("TAL混响")
        filter_layout.addRow("TAL混响滤镜名称:", self.tal_name_edit)
        
        # 选项
        self.auto_configure_cb = QCheckBox("自动配置默认参数")
        self.auto_configure_cb.setChecked(True)
        filter_layout.addRow(self.auto_configure_cb)
        
        self.replace_existing_cb = QCheckBox("替换已存在的滤镜")
        self.replace_existing_cb.setChecked(False)
        filter_layout.addRow(self.replace_existing_cb)
        
        layout.addWidget(filter_group)
        
        # 目标音频源
        source_group = QGroupBox("🎯 目标音频源")
        source_layout = QFormLayout(source_group)
        
        self.source_combo = QComboBox()
        self.source_combo.addItems(["麦克风", "桌面音频", "音频输入捕获"])
        source_layout.addRow("音频源:", self.source_combo)
        
        layout.addWidget(source_group)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        self.save_config_btn = QPushButton("💾 保存配置")
        self.save_config_btn.clicked.connect(self.save_settings)
        button_layout.addWidget(self.save_config_btn)
        
        self.test_paths_btn = QPushButton("🧪 测试路径")
        self.test_paths_btn.clicked.connect(self.test_plugin_paths)
        button_layout.addWidget(self.test_paths_btn)
        
        self.setup_filters_btn = QPushButton("🔧 自动设置滤镜")
        self.setup_filters_btn.clicked.connect(self.setup_vst_filters)
        self.setup_filters_btn.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12pt;
            }
            QPushButton:hover {
                background: linear-gradient(135deg, #059669 0%, #047857 100%);
            }
        """)
        button_layout.addWidget(self.setup_filters_btn)
        
        layout.addLayout(button_layout)
        
        # 日志显示
        layout.addWidget(QLabel("📊 操作日志:"))
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)
        
        self.log("🚀 VST自动设置工具已启动")
        
    def browse_vst_plugin(self, line_edit):
        """浏览VST插件文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择VST插件文件",
            "C:\\Program Files\\VSTPlugins",
            "VST插件 (*.dll);;所有文件 (*.*)"
        )
        
        if file_path:
            line_edit.setText(file_path)
            self.log(f"📁 选择插件: {os.path.basename(file_path)}")
            
    def auto_detect_plugins(self):
        """自动检测VST插件路径"""
        self.log("🔍 开始自动检测VST插件...")
        
        # 常见的VST插件路径
        search_paths = [
            "C:\\Program Files\\VSTPlugins",
            "C:\\Program Files (x86)\\VSTPlugins", 
            "C:\\Program Files\\Steinberg\\VSTPlugins",
            "C:\\Program Files (x86)\\Steinberg\\VSTPlugins",
            "C:\\Program Files\\Common Files\\VST2",
            "C:\\Program Files (x86)\\Common Files\\VST2"
        ]
        
        # 要查找的插件文件
        plugins_to_find = {
            "Auburn Sounds Graillon 3-64.dll": self.graillon_path_edit,
            "Graillon.dll": self.graillon_path_edit,
            "TSE_808_2.0_x64.dll": self.tse808_path_edit,
            "TSE808.dll": self.tse808_path_edit,
            "TAL-Reverb-4-64.dll": self.tal_path_edit,
            "TAL-Reverb.dll": self.tal_path_edit
        }
        
        found_count = 0
        
        for search_path in search_paths:
            if not os.path.exists(search_path):
                continue
                
            self.log(f"🔍 搜索路径: {search_path}")
            
            try:
                for root, dirs, files in os.walk(search_path):
                    for file in files:
                        if file in plugins_to_find:
                            full_path = os.path.join(root, file)
                            line_edit = plugins_to_find[file]
                            
                            if not line_edit.text():  # 只设置空的字段
                                line_edit.setText(full_path)
                                self.log(f"✅ 找到: {file}")
                                found_count += 1
                                
            except Exception as e:
                self.log(f"❌ 搜索 {search_path} 时出错: {e}")
        
        if found_count > 0:
            self.log(f"🎉 自动检测完成，找到 {found_count} 个插件")
        else:
            self.log("⚠️ 未找到任何VST插件，请手动选择")
            
    def test_plugin_paths(self):
        """测试插件路径是否有效"""
        self.log("🧪 测试VST插件路径...")
        
        plugins = {
            "Graillon 3-64": self.graillon_path_edit.text(),
            "TSE 808": self.tse808_path_edit.text(),
            "TAL Reverb": self.tal_path_edit.text()
        }
        
        valid_count = 0
        
        for name, path in plugins.items():
            if not path:
                self.log(f"⚠️ {name}: 路径为空")
                continue
                
            if os.path.exists(path):
                self.log(f"✅ {name}: 路径有效")
                valid_count += 1
            else:
                self.log(f"❌ {name}: 文件不存在 - {path}")
        
        if valid_count == len([p for p in plugins.values() if p]):
            self.log("🎉 所有插件路径测试通过！")
        else:
            self.log("⚠️ 部分插件路径无效，请检查")
            
    def setup_vst_filters(self):
        """设置VST滤镜"""
        self.log("🔧 开始自动设置VST滤镜...")
        
        # 检查路径
        if not all([self.graillon_path_edit.text(), self.tse808_path_edit.text(), self.tal_path_edit.text()]):
            QMessageBox.warning(self, "路径不完整", "请先设置所有VST插件路径")
            return
        
        # 生成配置代码
        config_code = self.generate_config_code()
        
        # 显示配置代码
        QMessageBox.information(
            self,
            "配置代码已生成",
            "VST滤镜配置代码已生成并复制到剪贴板。\n\n"
            "请将此代码集成到您的主程序中。"
        )
        
        # 复制到剪贴板
        clipboard = QApplication.clipboard()
        clipboard.setText(config_code)
        
        self.log("✅ 配置代码已生成并复制到剪贴板")
        
    def generate_config_code(self):
        """生成配置代码"""
        code = f'''
# VST插件自动配置
vst_plugins = {{
    "{self.graillon_name_edit.text()}": {{
        "plugin_path": r"{self.graillon_path_edit.text()}",
        "default_settings": {{
            "pitch": 0.0,
            "formant": 100.0,
            "mix": 100.0
        }}
    }},
    "{self.tse808_name_edit.text()}": {{
        "plugin_path": r"{self.tse808_path_edit.text()}", 
        "default_settings": {{
            "drive": 30.0,
            "tone": 50.0,
            "level": 80.0
        }}
    }},
    "{self.tal_name_edit.text()}": {{
        "plugin_path": r"{self.tal_path_edit.text()}",
        "default_settings": {{
            "roomsize": 40.0,
            "damping": 60.0,
            "mix": 25.0
        }}
    }}
}}

# 目标音频源
target_source = "{self.source_combo.currentText()}"

# 配置选项
auto_configure = {self.auto_configure_cb.isChecked()}
replace_existing = {self.replace_existing_cb.isChecked()}
'''
        return code
        
    def save_settings(self):
        """保存设置"""
        self.settings.setValue("graillon_path", self.graillon_path_edit.text())
        self.settings.setValue("tse808_path", self.tse808_path_edit.text())
        self.settings.setValue("tal_path", self.tal_path_edit.text())
        
        self.settings.setValue("graillon_name", self.graillon_name_edit.text())
        self.settings.setValue("tse808_name", self.tse808_name_edit.text())
        self.settings.setValue("tal_name", self.tal_name_edit.text())
        
        self.settings.setValue("auto_configure", self.auto_configure_cb.isChecked())
        self.settings.setValue("replace_existing", self.replace_existing_cb.isChecked())
        self.settings.setValue("target_source", self.source_combo.currentText())
        
        self.settings.sync()
        self.log("💾 设置已保存")
        
    def load_settings(self):
        """加载设置"""
        self.graillon_path_edit.setText(self.settings.value("graillon_path", ""))
        self.tse808_path_edit.setText(self.settings.value("tse808_path", ""))
        self.tal_path_edit.setText(self.settings.value("tal_path", ""))
        
        self.graillon_name_edit.setText(self.settings.value("graillon_name", "Graillon音调"))
        self.tse808_name_edit.setText(self.settings.value("tse808_name", "TSE808失真"))
        self.tal_name_edit.setText(self.settings.value("tal_name", "TAL混响"))
        
        self.auto_configure_cb.setChecked(self.settings.value("auto_configure", True, type=bool))
        self.replace_existing_cb.setChecked(self.settings.value("replace_existing", False, type=bool))
        
        target_source = self.settings.value("target_source", "麦克风")
        index = self.source_combo.findText(target_source)
        if index >= 0:
            self.source_combo.setCurrentIndex(index)
            
        self.log("📂 设置已加载")
        
    def log(self, message):
        """记录日志"""
        self.log_text.append(message)
        cursor = self.log_text.textCursor()
        cursor.movePosition(cursor.End)
        self.log_text.setTextCursor(cursor)
        print(message)

def main():
    app = QApplication(sys.argv)
    window = VSTAutoSetupTool()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
