#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VST自动控制功能测试脚本
测试新添加的VST自动控制Tab功能
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton, QTextEdit
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont

class VSTAutoControlTest(QMainWindow):
    """VST自动控制功能测试"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("VST自动控制功能测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 模拟VST控制状态
        self.vst_pitch_control = {
            "enabled": False,
            "source_name": "测试音频源",
            "filter_name": "Graillon音调",
            "min_pitch": -6.0,
            "max_pitch": 6.0,
            "interval_secs": 3.0,
            "timer": QTimer(self),
            "original_pitch": 0.0
        }
        
        self.vst_distortion_control = {
            "enabled": False,
            "source_name": "测试音频源",
            "filter_name": "TSE808失真",
            "min_drive": 10.0,
            "max_drive": 70.0,
            "min_tone": 20.0,
            "max_tone": 80.0,
            "interval_secs": 3.0,
            "timer": QTimer(self),
            "original_drive": 30.0,
            "original_tone": 50.0
        }
        
        self.vst_reverb_control = {
            "enabled": False,
            "source_name": "测试音频源",
            "filter_name": "TAL混响",
            "min_roomsize": 20.0,
            "max_roomsize": 60.0,
            "min_mix": 15.0,
            "max_mix": 35.0,
            "interval_secs": 4.0,
            "timer": QTimer(self),
            "original_roomsize": 40.0,
            "original_mix": 25.0
        }
        
        self.is_connected = True  # 模拟已连接状态
        
        self.setup_ui()
        self.setup_timers()
        
    def setup_ui(self):
        """设置UI界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title = QLabel("🎛️ VST自动控制功能测试")
        title.setAlignment(Qt.AlignCenter)
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setStyleSheet("""
            QLabel {
                color: #667eea;
                padding: 15px;
                background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                border-radius: 12px;
                border: 2px solid #e2e8f0;
            }
        """)
        layout.addWidget(title)
        
        # 功能说明
        desc = QLabel("""
        🎯 <b>测试功能：</b>验证VST自动控制Tab的UI和逻辑功能<br>
        🔧 <b>模拟插件：</b>Auburn Sounds Graillon 3-64 | TSE_808_2.0_x64 | TAL-Reverb-4-64<br>
        ⚡ <b>测试内容：</b>一键启动/停止、参数随机调节、状态显示
        """)
        desc.setWordWrap(True)
        desc.setStyleSheet("""
            QLabel {
                font-size: 11pt;
                color: #64748b;
                padding: 12px;
                background: #f1f5f9;
                border-radius: 8px;
                border-left: 4px solid #667eea;
            }
        """)
        layout.addWidget(desc)
        
        # 控制按钮
        button_layout = QVBoxLayout()
        
        self.start_all_btn = QPushButton("🚀 测试一键启动所有VST")
        self.start_all_btn.setStyleSheet("""
            QPushButton {
                font-size: 12pt;
                font-weight: bold;
                color: white;
                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                border: none;
                border-radius: 8px;
                padding: 12px 20px;
                margin: 5px;
            }
            QPushButton:hover {
                background: linear-gradient(135deg, #059669 0%, #047857 100%);
            }
        """)
        self.start_all_btn.clicked.connect(self.test_start_all_vst)
        button_layout.addWidget(self.start_all_btn)
        
        self.stop_all_btn = QPushButton("⏹️ 测试一键停止所有VST")
        self.stop_all_btn.setStyleSheet("""
            QPushButton {
                font-size: 12pt;
                font-weight: bold;
                color: white;
                background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
                border: none;
                border-radius: 8px;
                padding: 12px 20px;
                margin: 5px;
            }
            QPushButton:hover {
                background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            }
        """)
        self.stop_all_btn.clicked.connect(self.test_stop_all_vst)
        button_layout.addWidget(self.stop_all_btn)
        
        layout.addLayout(button_layout)
        
        # 日志显示区域
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(300)
        self.log_text.setStyleSheet("""
            QTextEdit {
                background: #1e293b;
                color: #e2e8f0;
                border: 2px solid #374151;
                border-radius: 8px;
                padding: 10px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 10pt;
            }
        """)
        layout.addWidget(self.log_text)
        
        # 初始日志
        self.log("🎛️ VST自动控制功能测试已启动")
        self.log("📋 支持的VST插件:")
        self.log("  🎵 Auburn Sounds Graillon 3-64 (音调控制)")
        self.log("  🔥 TSE_808_2.0_x64 (失真控制)")
        self.log("  🌊 TAL-Reverb-4-64 (混响控制)")
        self.log("=" * 50)
        
    def setup_timers(self):
        """设置定时器"""
        self.vst_pitch_control["timer"].timeout.connect(self.simulate_pitch_change)
        self.vst_distortion_control["timer"].timeout.connect(self.simulate_distortion_change)
        self.vst_reverb_control["timer"].timeout.connect(self.simulate_reverb_change)
        
    def log(self, message):
        """添加日志"""
        self.log_text.append(message)
        self.log_text.verticalScrollBar().setValue(
            self.log_text.verticalScrollBar().maximum()
        )
        
    def test_start_all_vst(self):
        """测试一键启动所有VST"""
        self.log("🚀 开始测试一键启动所有VST控制...")
        
        # 启动Graillon音调控制
        if not self.vst_pitch_control["enabled"]:
            self.vst_pitch_control["enabled"] = True
            interval_ms = int(self.vst_pitch_control["interval_secs"] * 1000)
            self.vst_pitch_control["timer"].start(interval_ms)
            self.log("✅ Graillon音调控制已启动")
        
        # 启动TSE808失真控制
        if not self.vst_distortion_control["enabled"]:
            self.vst_distortion_control["enabled"] = True
            interval_ms = int(self.vst_distortion_control["interval_secs"] * 1000)
            self.vst_distortion_control["timer"].start(interval_ms)
            self.log("✅ TSE808失真控制已启动")
        
        # 启动TAL混响控制
        if not self.vst_reverb_control["enabled"]:
            self.vst_reverb_control["enabled"] = True
            interval_ms = int(self.vst_reverb_control["interval_secs"] * 1000)
            self.vst_reverb_control["timer"].start(interval_ms)
            self.log("✅ TAL混响控制已启动")
        
        self.log("🎉 所有VST控制已启动！正在自动调节参数...")
        
    def test_stop_all_vst(self):
        """测试一键停止所有VST"""
        self.log("⏹️ 开始测试一键停止所有VST控制...")
        
        # 停止所有定时器
        if self.vst_pitch_control["enabled"]:
            self.vst_pitch_control["enabled"] = False
            self.vst_pitch_control["timer"].stop()
            self.log("⏹️ Graillon音调控制已停止")
        
        if self.vst_distortion_control["enabled"]:
            self.vst_distortion_control["enabled"] = False
            self.vst_distortion_control["timer"].stop()
            self.log("⏹️ TSE808失真控制已停止")
        
        if self.vst_reverb_control["enabled"]:
            self.vst_reverb_control["enabled"] = False
            self.vst_reverb_control["timer"].stop()
            self.log("⏹️ TAL混响控制已停止")
        
        self.log("✅ 所有VST控制已停止")
        
    def simulate_pitch_change(self):
        """模拟音调变化"""
        if not self.vst_pitch_control["enabled"]:
            return
            
        import random
        min_pitch = self.vst_pitch_control["min_pitch"]
        max_pitch = self.vst_pitch_control["max_pitch"]
        random_pitch = random.uniform(min_pitch, max_pitch)
        
        self.log(f"🎵 Graillon音调调整: {random_pitch:.1f} 半音")
        
    def simulate_distortion_change(self):
        """模拟失真变化"""
        if not self.vst_distortion_control["enabled"]:
            return
            
        import random
        min_drive = self.vst_distortion_control["min_drive"]
        max_drive = self.vst_distortion_control["max_drive"]
        min_tone = self.vst_distortion_control["min_tone"]
        max_tone = self.vst_distortion_control["max_tone"]
        
        random_drive = random.uniform(min_drive, max_drive)
        random_tone = random.uniform(min_tone, max_tone)
        
        self.log(f"🔥 TSE808失真调整: Drive={random_drive:.1f}%, Tone={random_tone:.1f}%")
        
    def simulate_reverb_change(self):
        """模拟混响变化"""
        if not self.vst_reverb_control["enabled"]:
            return
            
        import random
        min_roomsize = self.vst_reverb_control["min_roomsize"]
        max_roomsize = self.vst_reverb_control["max_roomsize"]
        min_mix = self.vst_reverb_control["min_mix"]
        max_mix = self.vst_reverb_control["max_mix"]
        
        random_roomsize = random.uniform(min_roomsize, max_roomsize)
        random_mix = random.uniform(min_mix, max_mix)
        
        self.log(f"🌊 TAL混响调整: Room={random_roomsize:.1f}%, Mix={random_mix:.1f}%")
        
    def closeEvent(self, event):
        """关闭事件"""
        # 停止所有定时器
        self.vst_pitch_control["timer"].stop()
        self.vst_distortion_control["timer"].stop()
        self.vst_reverb_control["timer"].stop()
        event.accept()

def main():
    app = QApplication(sys.argv)
    window = VSTAutoControlTest()
    window.show()
    
    print("🎛️ VST自动控制功能测试启动")
    print("=" * 50)
    print("✅ 功能已实现:")
    print("  🎵 Auburn Sounds Graillon 3-64 音调控制")
    print("  🔥 TSE_808_2.0_x64 失真控制")
    print("  🌊 TAL-Reverb-4-64 混响控制")
    print("  ⚡ 一键启动/停止所有VST")
    print("  🎛️ 统一的VST自动控制界面")
    print("=" * 50)
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
