# -*- coding: utf-8 -*-
"""
VST参数检测工具
帮助检测OBS中VST 2.x滤镜的参数名称和值
"""

import sys
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QLineEdit, QTextEdit, QComboBox,
    QGroupBox, QFormLayout, QSpinBox
)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont

# 模拟OBS连接（实际使用时需要真实的OBS连接）
class MockOBSClient:
    def __init__(self):
        self.connected = True
        # 模拟VST滤镜参数
        self.mock_filters = {
            "Graillon音调": {
                "filterKind": "vst_filter",
                "filterSettings": {
                    "pitch": 0.0,
                    "formant": 100.0,
                    "mix": 100.0,
                    "param_0": 0.0,  # 可能的参数格式
                    "param_1": 100.0,
                    "param_2": 100.0
                }
            },
            "TSE808失真": {
                "filterKind": "vst_filter", 
                "filterSettings": {
                    "drive": 30.0,
                    "tone": 50.0,
                    "level": 80.0,
                    "Drive": 30.0,  # 大写版本
                    "Tone": 50.0,
                    "Level": 80.0
                }
            },
            "TAL混响": {
                "filterKind": "vst_filter",
                "filterSettings": {
                    "roomsize": 40.0,
                    "damping": 60.0,
                    "mix": 25.0,
                    "RoomSize": 40.0,
                    "Damping": 60.0,
                    "Mix": 25.0
                }
            }
        }
    
    def call(self, request):
        class MockResponse:
            def __init__(self, success=True, data=None):
                self.ok = success
                self.datain = data or {}
        
        # 模拟获取滤镜列表
        if hasattr(request, 'sourceName') and not hasattr(request, 'filterName'):
            filters = []
            for name, info in self.mock_filters.items():
                filters.append({
                    "filterName": name,
                    "filterKind": info["filterKind"]
                })
            return MockResponse(True, {"filters": filters})
        
        # 模拟获取单个滤镜
        if hasattr(request, 'filterName'):
            filter_name = request.filterName
            if filter_name in self.mock_filters:
                return MockResponse(True, self.mock_filters[filter_name])
        
        return MockResponse(False)

class VSTParameterDetector(QMainWindow):
    """VST参数检测工具"""
    
    def __init__(self):
        super().__init__()
        self.obs_client = MockOBSClient()
        self.obs_connected = True
        self.setup_ui()
        
    def setup_ui(self):
        """设置界面"""
        self.setWindowTitle("🔍 VST参数检测工具")
        self.setGeometry(100, 100, 800, 600)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title = QLabel("🔍 VST 2.x滤镜参数检测工具")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 说明
        info = QLabel("此工具帮助您检测OBS中VST 2.x滤镜的参数名称和当前值")
        info.setAlignment(Qt.AlignCenter)
        info.setStyleSheet("color: #666; margin: 10px;")
        layout.addWidget(info)
        
        # 检测设置
        detect_group = QGroupBox("🎯 检测设置")
        detect_layout = QFormLayout(detect_group)
        
        self.source_name_edit = QLineEdit("麦克风")
        detect_layout.addRow("音频源名称:", self.source_name_edit)
        
        self.filter_name_edit = QLineEdit("Graillon音调")
        detect_layout.addRow("VST滤镜名称:", self.filter_name_edit)
        
        layout.addWidget(detect_group)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        self.list_filters_btn = QPushButton("📋 列出所有滤镜")
        self.list_filters_btn.clicked.connect(self.list_all_filters)
        button_layout.addWidget(self.list_filters_btn)
        
        self.detect_params_btn = QPushButton("🔍 检测参数")
        self.detect_params_btn.clicked.connect(self.detect_parameters)
        button_layout.addWidget(self.detect_params_btn)
        
        self.test_control_btn = QPushButton("🧪 测试控制")
        self.test_control_btn.clicked.connect(self.test_parameter_control)
        button_layout.addWidget(self.test_control_btn)
        
        layout.addLayout(button_layout)
        
        # 参数测试区域
        test_group = QGroupBox("🎛️ 参数测试")
        test_layout = QFormLayout(test_group)
        
        self.param_name_edit = QLineEdit("pitch")
        test_layout.addRow("参数名称:", self.param_name_edit)
        
        param_value_layout = QHBoxLayout()
        self.param_value_spin = QSpinBox()
        self.param_value_spin.setRange(-100, 100)
        self.param_value_spin.setValue(0)
        
        self.set_param_btn = QPushButton("设置参数")
        self.set_param_btn.clicked.connect(self.set_test_parameter)
        
        param_value_layout.addWidget(self.param_value_spin)
        param_value_layout.addWidget(self.set_param_btn)
        
        test_layout.addRow("参数值:", param_value_layout)
        
        layout.addWidget(test_group)
        
        # 结果显示
        layout.addWidget(QLabel("📊 检测结果:"))
        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        layout.addWidget(self.result_text)
        
        self.log("🚀 VST参数检测工具已启动")
        self.log("ℹ️ 这是模拟版本，实际使用需要连接到OBS")
        
    def list_all_filters(self):
        """列出所有滤镜"""
        try:
            source_name = self.source_name_edit.text()
            self.log(f"🔍 检测音频源 '{source_name}' 的所有滤镜...")
            
            # 模拟OBS API调用
            filters = [
                {"filterName": "Graillon音调", "filterKind": "vst_filter"},
                {"filterName": "TSE808失真", "filterKind": "vst_filter"},
                {"filterName": "TAL混响", "filterKind": "vst_filter"},
                {"filterName": "噪音抑制", "filterKind": "noise_suppress_filter"},
                {"filterName": "增益", "filterKind": "gain_filter"}
            ]
            
            self.log("📋 找到的滤镜列表:")
            for filter_info in filters:
                filter_name = filter_info["filterName"]
                filter_kind = filter_info["filterKind"]
                
                if filter_kind == "vst_filter":
                    self.log(f"  ✅ VST滤镜: '{filter_name}'")
                else:
                    self.log(f"  ⚪ 其他滤镜: '{filter_name}' (类型: {filter_kind})")
                    
        except Exception as e:
            self.log(f"❌ 列出滤镜失败: {e}")
            
    def detect_parameters(self):
        """检测VST滤镜参数"""
        try:
            source_name = self.source_name_edit.text()
            filter_name = self.filter_name_edit.text()
            
            self.log(f"🔍 检测VST滤镜 '{filter_name}' 的参数...")
            
            # 模拟获取滤镜设置
            if filter_name in self.obs_client.mock_filters:
                settings = self.obs_client.mock_filters[filter_name]["filterSettings"]
                
                self.log(f"📊 VST滤镜 '{filter_name}' 的所有参数:")
                self.log("=" * 50)
                
                for param_name, param_value in settings.items():
                    param_type = type(param_value).__name__
                    self.log(f"  📌 {param_name}: {param_value} ({param_type})")
                
                self.log("=" * 50)
                
                # 分析参数类型
                self.analyze_parameters(settings)
                
            else:
                self.log(f"❌ 未找到滤镜 '{filter_name}'")
                
        except Exception as e:
            self.log(f"❌ 检测参数失败: {e}")
            
    def analyze_parameters(self, settings):
        """分析参数类型"""
        self.log("🧠 参数分析:")
        
        # 常见的音调参数
        pitch_params = ["pitch", "semitones", "transpose", "Pitch", "Semitones"]
        # 常见的失真参数
        drive_params = ["drive", "gain", "distortion", "Drive", "Gain"]
        # 常见的音色参数
        tone_params = ["tone", "color", "timbre", "Tone", "Color"]
        # 常见的混响参数
        reverb_params = ["roomsize", "room", "size", "RoomSize", "Room"]
        
        for param_name in settings.keys():
            param_suggestions = []
            
            if any(p in param_name.lower() for p in [p.lower() for p in pitch_params]):
                param_suggestions.append("🎵 可能是音调控制参数")
                
            if any(p in param_name.lower() for p in [p.lower() for p in drive_params]):
                param_suggestions.append("🔥 可能是失真/增益参数")
                
            if any(p in param_name.lower() for p in [p.lower() for p in tone_params]):
                param_suggestions.append("🎛️ 可能是音色控制参数")
                
            if any(p in param_name.lower() for p in [p.lower() for p in reverb_params]):
                param_suggestions.append("🌊 可能是混响参数")
                
            if param_name.startswith("param_"):
                param_suggestions.append("🔢 通用参数格式")
                
            if param_suggestions:
                self.log(f"  {param_name}: {', '.join(param_suggestions)}")
                
    def test_parameter_control(self):
        """测试参数控制"""
        param_name = self.param_name_edit.text()
        param_value = self.param_value_spin.value()
        filter_name = self.filter_name_edit.text()
        
        self.log(f"🧪 测试设置参数 '{param_name}' = {param_value}")
        
        # 模拟参数设置
        if filter_name in self.obs_client.mock_filters:
            settings = self.obs_client.mock_filters[filter_name]["filterSettings"]
            
            if param_name in settings:
                old_value = settings[param_name]
                settings[param_name] = param_value
                self.log(f"✅ 参数设置成功: '{param_name}' {old_value} → {param_value}")
            else:
                self.log(f"⚠️ 参数 '{param_name}' 不存在，创建新参数")
                settings[param_name] = param_value
        else:
            self.log(f"❌ 滤镜 '{filter_name}' 不存在")
            
    def set_test_parameter(self):
        """设置测试参数"""
        self.test_parameter_control()
        
    def log(self, message):
        """记录日志"""
        self.result_text.append(message)
        cursor = self.result_text.textCursor()
        cursor.movePosition(cursor.End)
        self.result_text.setTextCursor(cursor)
        print(message)

def main():
    app = QApplication(sys.argv)
    window = VSTParameterDetector()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
