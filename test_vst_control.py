# -*- coding: utf-8 -*-
"""
VST控制功能测试脚本
用于测试音调和失真控制是否正常工作
"""

import sys
import random
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QLineEdit, QDoubleSpinBox, QTextEdit,
    QGroupBox, QFormLayout, QCheckBox, QTabWidget
)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont

# 模拟OBS连接（实际使用时需要真实的OBS连接）
class MockOBSClient:
    def __init__(self):
        self.connected = False
        self.sources = {
            "麦克风": {
                "filters": {
                    "VSR2X音调": {
                        "settings": {"pitch": 0.0}
                    },
                    "VSR2X失真": {
                        "settings": {"drive": 0.0, "tone": 50.0}
                    }
                }
            }
        }
    
    def call(self, request):
        # 模拟OBS响应
        class MockResponse:
            def __init__(self, success=True, data=None):
                self.ok = success
                self.datain = data or {}
        
        # 根据请求类型返回不同响应
        if hasattr(request, 'sourceName') and hasattr(request, 'filterName'):
            source_name = request.sourceName
            filter_name = request.filterName
            
            if source_name in self.sources and filter_name in self.sources[source_name]["filters"]:
                filter_data = self.sources[source_name]["filters"][filter_name]
                return MockResponse(True, {
                    "filterSettings": filter_data["settings"]
                })
        
        return MockResponse(False)

class VSTControlTest(QMainWindow):
    """VST控制功能测试"""
    
    def __init__(self):
        super().__init__()
        self.obs_client = MockOBSClient()
        self.obs_connected = True
        
        # VST控制状态
        self.vst_pitch_control = {
            "enabled": False,
            "source_name": "麦克风",
            "filter_name": "VSR2X音调",
            "min_pitch": -12.0,
            "max_pitch": 12.0,
            "interval_secs": 2.0,
            "timer": QTimer(self),
            "original_pitch": 0.0
        }
        
        self.vst_distortion_control = {
            "enabled": False,
            "source_name": "麦克风",
            "filter_name": "VSR2X失真",
            "min_drive": 0.0,
            "max_drive": 100.0,
            "min_tone": 0.0,
            "max_tone": 100.0,
            "interval_secs": 3.0,
            "timer": QTimer(self),
            "original_drive": 0.0,
            "original_tone": 50.0
        }
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置界面"""
        self.setWindowTitle("🎛️ VST控制功能测试")
        self.setGeometry(100, 100, 700, 600)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title = QLabel("🎛️ VST音调和失真控制测试")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 选项卡
        tab_widget = QTabWidget()
        
        # 音调控制选项卡
        pitch_tab = QWidget()
        pitch_layout = QFormLayout(pitch_tab)
        
        self.pitch_enabled_cb = QCheckBox("启用VST音调控制")
        self.pitch_enabled_cb.stateChanged.connect(self.toggle_pitch_control)
        pitch_layout.addRow(self.pitch_enabled_cb)
        
        self.pitch_filter_name_edit = QLineEdit("VSR2X音调")
        pitch_layout.addRow("滤镜名称:", self.pitch_filter_name_edit)
        
        pitch_range_widget = QWidget()
        pitch_range_layout = QHBoxLayout(pitch_range_widget)
        
        self.pitch_min_spin = QDoubleSpinBox()
        self.pitch_min_spin.setRange(-24.0, 24.0)
        self.pitch_min_spin.setValue(-12.0)
        self.pitch_min_spin.setSuffix(" 半音")
        
        self.pitch_max_spin = QDoubleSpinBox()
        self.pitch_max_spin.setRange(-24.0, 24.0)
        self.pitch_max_spin.setValue(12.0)
        self.pitch_max_spin.setSuffix(" 半音")
        
        pitch_range_layout.addWidget(QLabel("最小:"))
        pitch_range_layout.addWidget(self.pitch_min_spin)
        pitch_range_layout.addWidget(QLabel("最大:"))
        pitch_range_layout.addWidget(self.pitch_max_spin)
        
        pitch_layout.addRow("音调范围:", pitch_range_widget)
        
        self.pitch_interval_spin = QDoubleSpinBox()
        self.pitch_interval_spin.setRange(0.1, 60.0)
        self.pitch_interval_spin.setValue(2.0)
        self.pitch_interval_spin.setSuffix(" 秒")
        pitch_layout.addRow("变化间隔:", self.pitch_interval_spin)
        
        tab_widget.addTab(pitch_tab, "🎵 音调控制")
        
        # 失真控制选项卡
        distortion_tab = QWidget()
        distortion_layout = QFormLayout(distortion_tab)
        
        self.distortion_enabled_cb = QCheckBox("启用VST失真控制")
        self.distortion_enabled_cb.stateChanged.connect(self.toggle_distortion_control)
        distortion_layout.addRow(self.distortion_enabled_cb)
        
        self.distortion_filter_name_edit = QLineEdit("VSR2X失真")
        distortion_layout.addRow("滤镜名称:", self.distortion_filter_name_edit)
        
        # 失真强度范围
        drive_range_widget = QWidget()
        drive_range_layout = QHBoxLayout(drive_range_widget)
        
        self.drive_min_spin = QDoubleSpinBox()
        self.drive_min_spin.setRange(0.0, 100.0)
        self.drive_min_spin.setValue(0.0)
        self.drive_min_spin.setSuffix(" %")
        
        self.drive_max_spin = QDoubleSpinBox()
        self.drive_max_spin.setRange(0.0, 100.0)
        self.drive_max_spin.setValue(100.0)
        self.drive_max_spin.setSuffix(" %")
        
        drive_range_layout.addWidget(QLabel("最小:"))
        drive_range_layout.addWidget(self.drive_min_spin)
        drive_range_layout.addWidget(QLabel("最大:"))
        drive_range_layout.addWidget(self.drive_max_spin)
        
        distortion_layout.addRow("失真强度:", drive_range_widget)
        
        # 音色范围
        tone_range_widget = QWidget()
        tone_range_layout = QHBoxLayout(tone_range_widget)
        
        self.tone_min_spin = QDoubleSpinBox()
        self.tone_min_spin.setRange(0.0, 100.0)
        self.tone_min_spin.setValue(0.0)
        self.tone_min_spin.setSuffix(" %")
        
        self.tone_max_spin = QDoubleSpinBox()
        self.tone_max_spin.setRange(0.0, 100.0)
        self.tone_max_spin.setValue(100.0)
        self.tone_max_spin.setSuffix(" %")
        
        tone_range_layout.addWidget(QLabel("最小:"))
        tone_range_layout.addWidget(self.tone_min_spin)
        tone_range_layout.addWidget(QLabel("最大:"))
        tone_range_layout.addWidget(self.tone_max_spin)
        
        distortion_layout.addRow("音色调节:", tone_range_widget)
        
        self.distortion_interval_spin = QDoubleSpinBox()
        self.distortion_interval_spin.setRange(0.1, 60.0)
        self.distortion_interval_spin.setValue(3.0)
        self.distortion_interval_spin.setSuffix(" 秒")
        distortion_layout.addRow("变化间隔:", self.distortion_interval_spin)
        
        tab_widget.addTab(distortion_tab, "🔥 失真控制")
        
        layout.addWidget(tab_widget)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        self.test_pitch_btn = QPushButton("🧪 测试音调变化")
        self.test_pitch_btn.clicked.connect(self.test_pitch_change)
        button_layout.addWidget(self.test_pitch_btn)
        
        self.test_distortion_btn = QPushButton("🧪 测试失真变化")
        self.test_distortion_btn.clicked.connect(self.test_distortion_change)
        button_layout.addWidget(self.test_distortion_btn)
        
        self.stop_all_btn = QPushButton("⏹️ 停止所有")
        self.stop_all_btn.clicked.connect(self.stop_all_controls)
        button_layout.addWidget(self.stop_all_btn)
        
        layout.addLayout(button_layout)
        
        # 日志显示
        layout.addWidget(QLabel("📊 测试日志:"))
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)
        
        self.log("🚀 VST控制测试程序已启动")
        self.log("ℹ️ 这是模拟测试，实际使用需要连接到OBS")
        
    def toggle_pitch_control(self, state):
        """切换音调控制"""
        enabled = state == Qt.Checked
        self.vst_pitch_control["enabled"] = enabled
        
        if enabled:
            interval_ms = int(self.pitch_interval_spin.value() * 1000)
            self.vst_pitch_control["timer"].timeout.connect(self.apply_random_pitch)
            self.vst_pitch_control["timer"].start(interval_ms)
            self.log("✅ VST音调控制已启用")
        else:
            self.vst_pitch_control["timer"].stop()
            self.vst_pitch_control["timer"].timeout.disconnect()
            self.log("⏹️ VST音调控制已停用")
            
    def toggle_distortion_control(self, state):
        """切换失真控制"""
        enabled = state == Qt.Checked
        self.vst_distortion_control["enabled"] = enabled
        
        if enabled:
            interval_ms = int(self.distortion_interval_spin.value() * 1000)
            self.vst_distortion_control["timer"].timeout.connect(self.apply_random_distortion)
            self.vst_distortion_control["timer"].start(interval_ms)
            self.log("✅ VST失真控制已启用")
        else:
            self.vst_distortion_control["timer"].stop()
            self.vst_distortion_control["timer"].timeout.disconnect()
            self.log("⏹️ VST失真控制已停用")
            
    def apply_random_pitch(self):
        """应用随机音调变化"""
        min_pitch = self.pitch_min_spin.value()
        max_pitch = self.pitch_max_spin.value()
        random_pitch = random.uniform(min_pitch, max_pitch)
        
        # 模拟设置VST参数
        self.log(f"🎵 音调调整为: {random_pitch:.1f} 半音")
        
    def apply_random_distortion(self):
        """应用随机失真变化"""
        min_drive = self.drive_min_spin.value()
        max_drive = self.drive_max_spin.value()
        min_tone = self.tone_min_spin.value()
        max_tone = self.tone_max_spin.value()
        
        random_drive = random.uniform(min_drive, max_drive)
        random_tone = random.uniform(min_tone, max_tone)
        
        # 模拟设置VST参数
        self.log(f"🔥 失真调整 - 强度: {random_drive:.1f}%, 音色: {random_tone:.1f}%")
        
    def test_pitch_change(self):
        """测试单次音调变化"""
        self.apply_random_pitch()
        
    def test_distortion_change(self):
        """测试单次失真变化"""
        self.apply_random_distortion()
        
    def stop_all_controls(self):
        """停止所有控制"""
        self.pitch_enabled_cb.setChecked(False)
        self.distortion_enabled_cb.setChecked(False)
        self.log("⏹️ 所有VST控制已停止")
        
    def log(self, message):
        """记录日志"""
        self.log_text.append(message)
        cursor = self.log_text.textCursor()
        cursor.movePosition(cursor.End)
        self.log_text.setTextCursor(cursor)
        print(message)
        
    def closeEvent(self, event):
        """关闭事件"""
        self.stop_all_controls()
        event.accept()

def main():
    app = QApplication(sys.argv)
    window = VSTControlTest()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
