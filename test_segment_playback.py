# -*- coding: utf-8 -*-
"""
测试片段播放功能
验证音频播放控制是否正常工作
"""

import sys
import numpy as np
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QComboBox, QPushButton, QLabel, QTextEdit, QSpinBox, QGroupBox
)
from PyQt5.QtCore import Qt, QIODevice, QByteArray, QBuffer, QTimer
from PyQt5.QtMultimedia import QAudioDeviceInfo, QAudio, QAudioOutput, QAudioFormat

class SegmentPlaybackTest(QMainWindow):
    """片段播放测试"""
    
    def __init__(self):
        super().__init__()
        self.selected_device = None
        self.audio_output = None
        self.audio_buffer = None
        self.segment_timer = QTimer()
        self.segment_timer.setSingleShot(True)
        self.segment_timer.timeout.connect(self.stop_segment)
        self.setup_ui()
        self.populate_devices()
        
    def setup_ui(self):
        """设置界面"""
        self.setWindowTitle("🎵 片段播放测试")
        self.setGeometry(100, 100, 600, 500)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 设备选择组
        device_group = QGroupBox("🎯 音频设备")
        device_layout = QVBoxLayout(device_group)
        
        device_layout.addWidget(QLabel("选择音频输出设备:"))
        self.device_combo = QComboBox()
        self.device_combo.currentTextChanged.connect(self.on_device_changed)
        device_layout.addWidget(self.device_combo)
        
        layout.addWidget(device_group)
        
        # 播放控制组
        control_group = QGroupBox("🎮 播放控制")
        control_layout = QVBoxLayout(control_group)
        
        # 播放模式
        mode_layout = QHBoxLayout()
        mode_layout.addWidget(QLabel("播放模式:"))
        self.mode_combo = QComboBox()
        self.mode_combo.addItems(["完整播放", "片段播放"])
        self.mode_combo.currentTextChanged.connect(self.on_mode_changed)
        mode_layout.addWidget(self.mode_combo)
        control_layout.addLayout(mode_layout)
        
        # 片段时长
        duration_layout = QHBoxLayout()
        duration_layout.addWidget(QLabel("片段时长:"))
        self.duration_spin = QSpinBox()
        self.duration_spin.setRange(1, 10)
        self.duration_spin.setValue(3)
        self.duration_spin.setSuffix(" 秒")
        duration_layout.addWidget(self.duration_spin)
        duration_layout.addStretch()
        control_layout.addLayout(duration_layout)
        
        # 音频参数
        audio_layout = QHBoxLayout()
        audio_layout.addWidget(QLabel("测试频率:"))
        self.freq_spin = QSpinBox()
        self.freq_spin.setRange(200, 1000)
        self.freq_spin.setValue(440)
        self.freq_spin.setSuffix(" Hz")
        audio_layout.addWidget(self.freq_spin)
        audio_layout.addStretch()
        control_layout.addLayout(audio_layout)
        
        layout.addWidget(control_group)
        
        # 按钮组
        button_layout = QHBoxLayout()
        
        self.play_btn = QPushButton("▶️ 开始播放")
        self.play_btn.clicked.connect(self.start_playback)
        button_layout.addWidget(self.play_btn)
        
        self.stop_btn = QPushButton("⏹️ 停止播放")
        self.stop_btn.clicked.connect(self.stop_playback)
        button_layout.addWidget(self.stop_btn)
        
        layout.addLayout(button_layout)
        
        # 状态显示
        layout.addWidget(QLabel("📊 播放状态:"))
        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(200)
        self.status_text.setReadOnly(True)
        layout.addWidget(self.status_text)
        
        # 初始状态
        self.on_mode_changed("完整播放")
        
    def populate_devices(self):
        """填充设备列表"""
        self.device_combo.clear()
        self.log("🔍 检测音频输出设备...")
        
        try:
            devices = QAudioDeviceInfo.availableDevices(QAudio.AudioOutput)
            
            for i, device in enumerate(devices):
                if not device.isNull():
                    device_name = device.deviceName()
                    self.device_combo.addItem(f"{i+1}. {device_name}", device)
                    self.log(f"✅ 设备 {i+1}: {device_name}")
            
            if self.device_combo.count() > 0:
                self.device_combo.setCurrentIndex(0)
                
        except Exception as e:
            self.log(f"❌ 检测设备失败: {e}")
            
    def on_device_changed(self, device_name):
        """设备选择改变"""
        self.selected_device = self.device_combo.currentData()
        if self.selected_device:
            self.log(f"🎯 选择设备: {self.selected_device.deviceName()}")
        else:
            self.log("⚠️ 未选择有效设备")
            
    def on_mode_changed(self, mode):
        """播放模式改变"""
        if mode == "片段播放":
            self.duration_spin.setEnabled(True)
            self.log("🔄 切换到片段播放模式")
        else:
            self.duration_spin.setEnabled(False)
            self.log("🔄 切换到完整播放模式")
            
    def generate_test_audio(self, duration):
        """生成测试音频"""
        sample_rate = 44100
        frequency = self.freq_spin.value()
        
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        audio_data = 0.3 * np.sin(2 * np.pi * frequency * t)
        
        # 转换为立体声
        stereo_audio = np.column_stack((audio_data, audio_data))
        return stereo_audio, sample_rate
        
    def start_playback(self):
        """开始播放"""
        if not self.selected_device:
            self.log("❌ 请先选择设备")
            return
            
        try:
            mode = self.mode_combo.currentText()
            self.log(f"🎵 开始{mode}...")
            
            # 生成音频（完整播放用5秒，片段播放用设定时长）
            if mode == "片段播放":
                # 生成较长的音频，但只播放指定时长
                audio_duration = 10.0  # 生成10秒音频
                segment_duration = self.duration_spin.value()
                self.log(f"📊 生成 {audio_duration} 秒音频，播放 {segment_duration} 秒")
            else:
                audio_duration = 5.0
                self.log(f"📊 生成 {audio_duration} 秒音频，完整播放")
            
            audio_data, sample_rate = self.generate_test_audio(audio_duration)
            
            # 播放音频
            success = self.play_audio_through_device(audio_data, sample_rate)
            
            if success:
                if mode == "片段播放":
                    # 设置片段播放定时器
                    segment_ms = self.duration_spin.value() * 1000
                    self.segment_timer.start(segment_ms)
                    self.log(f"⏰ 设置 {self.duration_spin.value()} 秒后自动停止")
                else:
                    self.log("✅ 完整播放已开始")
            else:
                self.log("❌ 播放失败")
                
        except Exception as e:
            self.log(f"❌ 播放出错: {e}")
            
    def play_audio_through_device(self, audio_data, sample_rate):
        """通过指定设备播放音频"""
        try:
            # 停止之前的播放
            if self.audio_output:
                self.audio_output.stop()
                
            if self.audio_buffer:
                self.audio_buffer.close()
            
            # 转换音频格式
            audio_data_int16 = (audio_data * 32767).astype(np.int16)
            audio_bytes = audio_data_int16.tobytes()
            
            # 设置音频格式
            format = QAudioFormat()
            format.setSampleRate(sample_rate)
            format.setChannelCount(2)
            format.setSampleSize(16)
            format.setCodec("audio/pcm")
            format.setByteOrder(QAudioFormat.LittleEndian)
            format.setSampleType(QAudioFormat.SignedInt)
            
            if not self.selected_device.isFormatSupported(format):
                format = self.selected_device.nearestFormat(format)
            
            # 创建音频输出
            self.audio_output = QAudioOutput(self.selected_device, format)
            
            # 创建音频缓冲区
            self.audio_buffer = QBuffer()
            self.audio_buffer.setData(QByteArray(audio_bytes))
            self.audio_buffer.open(QIODevice.ReadOnly)
            
            # 开始播放
            self.audio_output.start(self.audio_buffer)
            
            state = self.audio_output.state()
            if state == QAudio.ActiveState or state == QAudio.IdleState:
                self.log(f"✅ 播放已开始: {self.selected_device.deviceName()}")
                return True
            else:
                self.log(f"❌ 播放失败，状态: {state}")
                return False
                
        except Exception as e:
            self.log(f"❌ 播放音频失败: {e}")
            return False
            
    def stop_segment(self):
        """停止片段播放（定时器触发）"""
        self.log("⏰ 片段播放时间到，自动停止")
        self.stop_playback()
        
    def stop_playback(self):
        """停止播放"""
        try:
            # 停止定时器
            if self.segment_timer.isActive():
                self.segment_timer.stop()
                self.log("⏰ 片段定时器已停止")
            
            # 停止音频播放
            if self.audio_output:
                self.audio_output.stop()
                self.log("⏹️ 音频播放已停止")
                
            if self.audio_buffer:
                self.audio_buffer.close()
                self.audio_buffer = None
                
        except Exception as e:
            self.log(f"❌ 停止播放失败: {e}")
            
    def log(self, message):
        """记录日志"""
        self.status_text.append(message)
        cursor = self.status_text.textCursor()
        cursor.movePosition(cursor.End)
        self.status_text.setTextCursor(cursor)
        print(message)
        
    def closeEvent(self, event):
        """关闭事件"""
        self.stop_playback()
        event.accept()

def main():
    app = QApplication(sys.argv)
    window = SegmentPlaybackTest()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
