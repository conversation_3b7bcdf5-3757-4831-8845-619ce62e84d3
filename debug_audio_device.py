# -*- coding: utf-8 -*-
"""
调试音频设备输出问题
逐步诊断为什么音频没有输出到指定设备
"""

import sys
import numpy as np
from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QPushButton, QComboBox, QLabel, QTextEdit
from PyQt5.QtCore import Qt, QIODevice, QByteArray, QBuffer
from PyQt5.QtMultimedia import QAudioDeviceInfo, QAudio, QAudioOutput, QAudioFormat

class AudioDeviceDebugger(QMainWindow):
    def __init__(self):
        super().__init__()
        self.selected_device = None
        self.audio_output = None
        self.audio_buffer = None
        self.setup_ui()
        self.populate_devices()
        
    def setup_ui(self):
        self.setWindowTitle("🔍 音频设备调试器")
        self.setGeometry(100, 100, 600, 500)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 设备选择
        layout.addWidget(QLabel("选择音频输出设备:"))
        self.device_combo = QComboBox()
        self.device_combo.currentTextChanged.connect(self.on_device_changed)
        layout.addWidget(self.device_combo)
        
        # 调试按钮
        self.debug_btn = QPushButton("🔍 开始调试")
        self.debug_btn.clicked.connect(self.start_debug)
        layout.addWidget(self.debug_btn)
        
        self.test_btn = QPushButton("🎵 测试播放")
        self.test_btn.clicked.connect(self.test_playback)
        layout.addWidget(self.test_btn)
        
        self.stop_btn = QPushButton("⏹️ 停止")
        self.stop_btn.clicked.connect(self.stop_audio)
        layout.addWidget(self.stop_btn)
        
        # 日志显示
        layout.addWidget(QLabel("调试信息:"))
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)
        
    def populate_devices(self):
        """填充设备列表"""
        self.device_combo.clear()
        self.log("🔍 检测音频输出设备...")
        
        try:
            # 获取默认设备
            default_device = QAudioDeviceInfo.defaultOutputDevice()
            if not default_device.isNull():
                self.device_combo.addItem(f"[默认] {default_device.deviceName()}", default_device)
                self.log(f"✅ 默认设备: {default_device.deviceName()}")
            
            # 获取所有设备
            devices = QAudioDeviceInfo.availableDevices(QAudio.AudioOutput)
            for i, device in enumerate(devices):
                if not device.isNull():
                    device_name = device.deviceName()
                    # 避免重复添加默认设备
                    if not default_device.isNull() and device_name != default_device.deviceName():
                        self.device_combo.addItem(f"[{i+1}] {device_name}", device)
                        self.log(f"✅ 设备 {i+1}: {device_name}")
                    elif default_device.isNull():
                        self.device_combo.addItem(f"[{i+1}] {device_name}", device)
                        self.log(f"✅ 设备 {i+1}: {device_name}")
            
            self.log(f"📊 总共找到 {self.device_combo.count()} 个设备")
            
        except Exception as e:
            self.log(f"❌ 检测设备失败: {e}")
            
    def on_device_changed(self, device_name):
        """设备选择改变"""
        self.selected_device = self.device_combo.currentData()
        if self.selected_device and not self.selected_device.isNull():
            self.log(f"🎯 选择设备: {self.selected_device.deviceName()}")
        else:
            self.log("⚠️ 未选择有效设备")
            
    def start_debug(self):
        """开始调试"""
        if not self.selected_device:
            self.log("❌ 请先选择设备")
            return
            
        self.log("=" * 50)
        self.log("🔍 开始音频设备调试")
        self.log("=" * 50)
        
        try:
            # 1. 检查设备基本信息
            self.log(f"📋 设备名称: {self.selected_device.deviceName()}")
            self.log(f"📋 设备是否为空: {self.selected_device.isNull()}")
            
            # 2. 检查设备支持的格式
            preferred_format = self.selected_device.preferredFormat()
            self.log(f"📋 首选格式:")
            self.log(f"  - 采样率: {preferred_format.sampleRate()} Hz")
            self.log(f"  - 声道数: {preferred_format.channelCount()}")
            self.log(f"  - 采样大小: {preferred_format.sampleSize()} bits")
            self.log(f"  - 编解码器: {preferred_format.codec()}")
            self.log(f"  - 字节序: {preferred_format.byteOrder()}")
            self.log(f"  - 采样类型: {preferred_format.sampleType()}")
            
            # 3. 测试标准PCM格式支持
            test_format = QAudioFormat()
            test_format.setSampleRate(44100)
            test_format.setChannelCount(2)
            test_format.setSampleSize(16)
            test_format.setCodec("audio/pcm")
            test_format.setByteOrder(QAudioFormat.LittleEndian)
            test_format.setSampleType(QAudioFormat.SignedInt)
            
            is_supported = self.selected_device.isFormatSupported(test_format)
            self.log(f"📋 标准PCM格式支持: {is_supported}")
            
            if not is_supported:
                nearest_format = self.selected_device.nearestFormat(test_format)
                self.log(f"📋 最接近格式:")
                self.log(f"  - 采样率: {nearest_format.sampleRate()} Hz")
                self.log(f"  - 声道数: {nearest_format.channelCount()}")
                self.log(f"  - 采样大小: {nearest_format.sampleSize()} bits")
            
            # 4. 尝试创建音频输出对象
            try:
                test_audio_output = QAudioOutput(self.selected_device, test_format if is_supported else nearest_format)
                self.log("✅ 音频输出对象创建成功")
                
                # 检查初始状态
                initial_state = test_audio_output.state()
                initial_error = test_audio_output.error()
                self.log(f"📋 初始状态: {initial_state}")
                self.log(f"📋 初始错误: {initial_error}")
                
                test_audio_output.stop()  # 清理
                
            except Exception as e:
                self.log(f"❌ 创建音频输出对象失败: {e}")
                
            self.log("=" * 50)
            self.log("🔍 调试完成，请查看上述信息")
            self.log("=" * 50)
            
        except Exception as e:
            self.log(f"❌ 调试过程出错: {e}")
            import traceback
            self.log(traceback.format_exc())
            
    def test_playback(self):
        """测试播放"""
        if not self.selected_device:
            self.log("❌ 请先选择设备")
            return
            
        try:
            self.log("🎵 开始测试播放...")
            
            # 生成2秒440Hz测试音频
            duration = 2.0
            sample_rate = 44100
            frequency = 440.0
            
            t = np.linspace(0, duration, int(sample_rate * duration), False)
            audio_data = 0.3 * np.sin(2 * np.pi * frequency * t)
            
            # 转换为立体声
            stereo_audio = np.column_stack((audio_data, audio_data))
            
            # 转换为16位整数
            audio_data_int16 = (stereo_audio * 32767).astype(np.int16)
            audio_bytes = audio_data_int16.tobytes()
            
            self.log(f"📊 生成音频: {len(audio_bytes)} 字节")
            
            # 设置音频格式
            format = QAudioFormat()
            format.setSampleRate(sample_rate)
            format.setChannelCount(2)
            format.setSampleSize(16)
            format.setCodec("audio/pcm")
            format.setByteOrder(QAudioFormat.LittleEndian)
            format.setSampleType(QAudioFormat.SignedInt)
            
            # 检查格式支持
            if not self.selected_device.isFormatSupported(format):
                self.log("⚠️ 格式不支持，使用最接近格式")
                format = self.selected_device.nearestFormat(format)
            
            # 停止之前的播放
            if self.audio_output:
                self.audio_output.stop()
                
            if self.audio_buffer:
                self.audio_buffer.close()
            
            # 创建音频输出
            self.audio_output = QAudioOutput(self.selected_device, format)
            
            # 创建音频缓冲区
            self.audio_buffer = QBuffer()
            self.audio_buffer.setData(QByteArray(audio_bytes))
            self.audio_buffer.open(QIODevice.ReadOnly)
            
            # 开始播放
            self.audio_output.start(self.audio_buffer)
            
            # 检查状态
            state = self.audio_output.state()
            error = self.audio_output.error()
            
            self.log(f"🔊 播放状态: {state}")
            self.log(f"🔊 错误状态: {error}")
            
            if state == QAudio.ActiveState:
                self.log("✅ 播放已开始！")
                self.log("🎧 请检查指定设备是否有声音输出")
                self.log(f"🎯 目标设备: {self.selected_device.deviceName()}")
            elif state == QAudio.IdleState:
                self.log("⚠️ 播放处于空闲状态")
            elif state == QAudio.StoppedState:
                self.log("❌ 播放已停止")
            else:
                self.log(f"❓ 未知播放状态: {state}")
                
            # 显示错误信息
            if error != QAudio.NoError:
                error_messages = {
                    QAudio.OpenError: "打开设备错误",
                    QAudio.IOError: "IO错误", 
                    QAudio.UnderrunError: "缓冲区不足错误",
                    QAudio.FatalError: "致命错误"
                }
                error_msg = error_messages.get(error, f"未知错误: {error}")
                self.log(f"❌ 错误详情: {error_msg}")
                
        except Exception as e:
            self.log(f"❌ 测试播放失败: {e}")
            import traceback
            self.log(traceback.format_exc())
            
    def stop_audio(self):
        """停止音频"""
        try:
            if self.audio_output:
                self.audio_output.stop()
                self.log("⏹️ 音频已停止")
                
            if self.audio_buffer:
                self.audio_buffer.close()
                self.audio_buffer = None
                
        except Exception as e:
            self.log(f"❌ 停止音频失败: {e}")
            
    def log(self, message):
        """记录日志"""
        self.log_text.append(message)
        cursor = self.log_text.textCursor()
        cursor.movePosition(cursor.End)
        self.log_text.setTextCursor(cursor)
        print(message)  # 同时输出到控制台

def main():
    app = QApplication(sys.argv)
    window = AudioDeviceDebugger()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
