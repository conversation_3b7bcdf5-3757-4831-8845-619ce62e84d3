# -*- coding: utf-8 -*-
"""
VST参数探测工具
找出VST插件的真实参数名称和值范围
"""

import sys
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QTextEdit, QLineEdit, QComboBox,
    QGroupBox, QFormLayout, QDoubleSpinBox
)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont

class VSTParameterExplorer(QMainWindow):
    """VST参数探测工具"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        
    def setup_ui(self):
        """设置界面"""
        self.setWindowTitle("🔍 VST参数探测工具")
        self.setGeometry(100, 100, 800, 700)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title = QLabel("🔍 VST参数探测工具")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 问题说明
        problem_group = QGroupBox("❓ 问题分析")
        problem_layout = QVBoxLayout(problem_group)
        
        problem_text = """
您遇到的问题：程序输出参数变化数值，但VST插件没有实际效果

可能的原因：
1. 🏷️ 参数名称不匹配 - VST插件的实际参数名与我们使用的不同
2. 📊 参数值范围不匹配 - VST插件期望的值范围与我们设置的不同  
3. 🔧 VST插件状态问题 - 插件没有正确加载或处于错误状态
4. 🎛️ 参数映射错误 - OBS中的参数映射与VST插件不一致
        """
        
        problem_label = QLabel(problem_text)
        problem_label.setWordWrap(True)
        problem_label.setStyleSheet("padding: 10px; background: #fff3cd; border-radius: 6px;")
        problem_layout.addWidget(problem_label)
        
        layout.addWidget(problem_group)
        
        # 探测设置
        explore_group = QGroupBox("🎯 参数探测设置")
        explore_layout = QFormLayout(explore_group)
        
        self.source_name_edit = QLineEdit("媒体源")
        explore_layout.addRow("音频源名称:", self.source_name_edit)
        
        self.filter_name_combo = QComboBox()
        self.filter_name_combo.addItems(["Graillon音调", "TSE808失真", "TAL混响"])
        explore_layout.addRow("VST滤镜:", self.filter_name_combo)
        
        layout.addWidget(explore_group)
        
        # 探测按钮
        button_layout = QHBoxLayout()
        
        self.explore_params_btn = QPushButton("🔍 探测所有参数")
        self.explore_params_btn.clicked.connect(self.explore_all_parameters)
        button_layout.addWidget(self.explore_params_btn)
        
        self.test_manual_btn = QPushButton("🧪 手动测试参数")
        self.test_manual_btn.clicked.connect(self.test_manual_parameter)
        button_layout.addWidget(self.test_manual_btn)
        
        self.compare_values_btn = QPushButton("📊 对比参数值")
        self.compare_values_btn.clicked.connect(self.compare_parameter_values)
        button_layout.addWidget(self.compare_values_btn)
        
        layout.addLayout(button_layout)
        
        # 手动测试区域
        manual_group = QGroupBox("🎛️ 手动参数测试")
        manual_layout = QFormLayout(manual_group)
        
        self.param_name_edit = QLineEdit("pitch")
        manual_layout.addRow("参数名称:", self.param_name_edit)
        
        self.param_value_spin = QDoubleSpinBox()
        self.param_value_spin.setRange(-100.0, 100.0)
        self.param_value_spin.setValue(0.0)
        self.param_value_spin.setSingleStep(0.1)
        manual_layout.addRow("参数值:", self.param_value_spin)
        
        self.set_param_btn = QPushButton("设置参数")
        self.set_param_btn.clicked.connect(self.set_single_parameter)
        manual_layout.addRow(self.set_param_btn)
        
        layout.addWidget(manual_group)
        
        # 结果显示
        layout.addWidget(QLabel("📋 探测结果:"))
        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        layout.addWidget(self.result_text)
        
        self.log("🚀 VST参数探测工具已启动")
        self.log("💡 此工具帮助找出VST插件的真实参数名称和值范围")
        
    def explore_all_parameters(self):
        """探测所有参数"""
        self.log("🔍 开始探测VST插件的所有参数...")
        
        source_name = self.source_name_edit.text()
        filter_name = self.filter_name_combo.currentText()
        
        self.log(f"📋 目标: 源='{source_name}', 滤镜='{filter_name}'")
        
        # 模拟探测结果（实际使用时需要连接OBS）
        if filter_name == "Graillon音调":
            self.explore_graillon_parameters()
        elif filter_name == "TSE808失真":
            self.explore_tse808_parameters()
        elif filter_name == "TAL混响":
            self.explore_tal_parameters()
            
    def explore_graillon_parameters(self):
        """探测Graillon参数"""
        self.log("🎵 探测 Auburn Sounds Graillon 3-64 参数...")
        
        # Graillon可能的参数名称和值范围
        possible_params = {
            "pitch": {"range": "半音 (-24 到 +24)", "current": "未知"},
            "Pitch": {"range": "半音 (-24 到 +24)", "current": "未知"},
            "pitch_shift": {"range": "半音 (-24 到 +24)", "current": "未知"},
            "formant": {"range": "百分比 (50-150%)", "current": "未知"},
            "Formant": {"range": "百分比 (50-150%)", "current": "未知"},
            "mix": {"range": "百分比 (0-100%)", "current": "未知"},
            "Mix": {"range": "百分比 (0-100%)", "current": "未知"},
            "param_0": {"range": "归一化 (0.0-1.0)", "current": "未知"},
            "param_1": {"range": "归一化 (0.0-1.0)", "current": "未知"},
            "param_2": {"range": "归一化 (0.0-1.0)", "current": "未知"},
        }
        
        self.log("📊 Graillon 可能的参数列表:")
        for param_name, info in possible_params.items():
            self.log(f"  - {param_name}: {info['range']}")
            
        self.log("\n💡 建议测试步骤:")
        self.log("1. 在OBS中打开Graillon插件界面")
        self.log("2. 手动调节Pitch参数，观察界面变化")
        self.log("3. 使用本工具逐个测试参数名称")
        self.log("4. 找到能影响插件界面的正确参数名")
        
    def explore_tse808_parameters(self):
        """探测TSE808参数"""
        self.log("🔥 探测 TSE_808_2.0_x64 参数...")
        
        possible_params = {
            "drive": {"range": "百分比 (0-100%)", "current": "未知"},
            "Drive": {"range": "百分比 (0-100%)", "current": "未知"},
            "tone": {"range": "百分比 (0-100%)", "current": "未知"},
            "Tone": {"range": "百分比 (0-100%)", "current": "未知"},
            "level": {"range": "百分比 (0-100%)", "current": "未知"},
            "Level": {"range": "百分比 (0-100%)", "current": "未知"},
            "gain": {"range": "dB (-20 到 +20)", "current": "未知"},
            "param_0": {"range": "归一化 (0.0-1.0)", "current": "未知"},
            "param_1": {"range": "归一化 (0.0-1.0)", "current": "未知"},
            "param_2": {"range": "归一化 (0.0-1.0)", "current": "未知"},
        }
        
        self.log("📊 TSE808 可能的参数列表:")
        for param_name, info in possible_params.items():
            self.log(f"  - {param_name}: {info['range']}")
            
    def explore_tal_parameters(self):
        """探测TAL混响参数"""
        self.log("🌊 探测 TAL-Reverb-4-64 参数...")
        
        possible_params = {
            "roomsize": {"range": "百分比 (0-100%)", "current": "未知"},
            "RoomSize": {"range": "百分比 (0-100%)", "current": "未知"},
            "room_size": {"range": "百分比 (0-100%)", "current": "未知"},
            "damping": {"range": "百分比 (0-100%)", "current": "未知"},
            "Damping": {"range": "百分比 (0-100%)", "current": "未知"},
            "mix": {"range": "百分比 (0-100%)", "current": "未知"},
            "Mix": {"range": "百分比 (0-100%)", "current": "未知"},
            "wet": {"range": "百分比 (0-100%)", "current": "未知"},
            "param_0": {"range": "归一化 (0.0-1.0)", "current": "未知"},
            "param_1": {"range": "归一化 (0.0-1.0)", "current": "未知"},
            "param_2": {"range": "归一化 (0.0-1.0)", "current": "未知"},
        }
        
        self.log("📊 TAL混响 可能的参数列表:")
        for param_name, info in possible_params.items():
            self.log(f"  - {param_name}: {info['range']}")
            
    def test_manual_parameter(self):
        """手动测试参数"""
        param_name = self.param_name_edit.text()
        param_value = self.param_value_spin.value()
        
        self.log(f"🧪 手动测试参数: '{param_name}' = {param_value}")
        self.log("📋 测试步骤:")
        self.log("1. 在OBS中打开VST插件界面")
        self.log("2. 观察插件界面的当前状态")
        self.log("3. 点击下面的'设置参数'按钮")
        self.log("4. 观察插件界面是否有变化")
        self.log("5. 如果有变化，说明参数名称正确")
        
    def set_single_parameter(self):
        """设置单个参数"""
        param_name = self.param_name_edit.text()
        param_value = self.param_value_spin.value()
        
        self.log(f"🔧 设置参数: '{param_name}' = {param_value}")
        self.log("⚠️ 注意：这是模拟操作，实际使用需要连接OBS")
        self.log("✅ 如果连接了OBS，此操作会直接设置VST参数")
        
    def compare_parameter_values(self):
        """对比参数值"""
        self.log("📊 参数值范围对比分析...")
        
        self.log("\n🎵 音调参数值范围对比:")
        self.log("  我们使用: 5.5 半音")
        self.log("  VST可能期望:")
        self.log("    - 归一化值: 0.0-1.0 (5.5半音 ≈ 0.6)")
        self.log("    - MIDI值: 0-127 (5.5半音 ≈ 69)")
        self.log("    - 直接半音: -24 到 +24")
        
        self.log("\n🔥 失真参数值范围对比:")
        self.log("  我们使用: 30.0%")
        self.log("  VST可能期望:")
        self.log("    - 归一化值: 0.0-1.0 (30% = 0.3)")
        self.log("    - 百分比: 0-100 (直接使用30)")
        self.log("    - dB值: -20 到 +20 (需要转换)")
        
        self.log("\n💡 建议:")
        self.log("1. 尝试不同的参数值范围")
        self.log("2. 观察VST插件界面的实际变化")
        self.log("3. 找到正确的值范围映射关系")
        
    def log(self, message):
        """记录日志"""
        self.result_text.append(message)
        cursor = self.result_text.textCursor()
        cursor.movePosition(cursor.End)
        self.result_text.setTextCursor(cursor)
        print(message)

def main():
    app = QApplication(sys.argv)
    window = VSTParameterExplorer()
    window.show()
    
    # 显示使用说明
    window.log("=" * 60)
    window.log("🎯 使用说明:")
    window.log("1. 确保OBS中已添加VST滤镜")
    window.log("2. 打开VST插件界面观察当前状态")
    window.log("3. 使用本工具探测和测试参数")
    window.log("4. 找到能实际影响插件的正确参数名称")
    window.log("5. 记录正确的参数名称和值范围")
    window.log("=" * 60)
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
