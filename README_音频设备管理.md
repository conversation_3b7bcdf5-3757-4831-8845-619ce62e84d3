# 🎵 现代化音频设备管理系统

## 📋 概述

这是一个现代化的音频设备管理系统，专为Qt 5应用程序设计，提供了专业级的音频输出设备选择和声道控制功能。系统采用现代化UI设计，支持指定音频输出设备和精确的声道控制。

## ✨ 主要特性

### 🔊 音频设备管理
- **智能设备检测**: 自动检测系统中所有可用的音频输出设备
- **设备信息显示**: 显示设备详细信息（采样率、声道数、位深度等）
- **实时设备刷新**: 支持热插拔设备的实时检测
- **多平台支持**: 支持Windows、macOS、Linux等主流操作系统

### 🎚️ 声道控制
- **立体声模式**: 标准的左右声道输出
- **单声道模式**: 支持仅左声道或仅右声道输出
- **声道平衡**: 精确的左右声道平衡调节
- **实时预览**: 声道配置的实时音频测试

### 🎨 现代化UI设计
- **科技感界面**: 采用现代化的渐变色彩和阴影效果
- **响应式布局**: 自适应不同屏幕尺寸和分辨率
- **直观操作**: 简洁明了的用户交互界面
- **状态反馈**: 实时的操作状态和错误提示

## 📁 文件结构

```
音频设备管理系统/
├── modern_audio_device_panel.py    # 现代化音频设备配置面板
├── audio_device_manager.py         # 音频设备管理器主窗口
├── audio_integration_demo.py       # 集成演示程序
├── modern_ui_components.py         # 现代化UI组件库
├── modern_ui_styles.py            # 现代化UI样式定义
└── README_音频设备管理.md          # 本文档
```

## 🚀 快速开始

### 环境要求

```bash
# Python 3.7+
# PyQt5
pip install PyQt5

# 音频处理库（可选，用于高级功能）
pip install soundfile numpy
```

### 基本使用

#### 1. 独立使用音频设备管理器

```python
from audio_device_manager import AudioDeviceManager
from PyQt5.QtWidgets import QApplication
import sys

app = QApplication(sys.argv)
manager = AudioDeviceManager()
manager.show()
app.exec_()
```

#### 2. 集成到现有应用

```python
from modern_audio_device_panel import ModernAudioDevicePanel

class YourMainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        
        # 创建音频设备面板
        self.audio_panel = ModernAudioDevicePanel()
        
        # 连接信号
        self.audio_panel.device_changed.connect(self.on_device_changed)
        self.audio_panel.channel_changed.connect(self.on_channel_changed)
        
    def on_device_changed(self, device):
        """设备改变事件处理"""
        print(f"设备已切换到: {device.deviceName() if device else '系统默认'}")
        
    def on_channel_changed(self, channel_mode):
        """声道改变事件处理"""
        modes = ["立体声", "仅左声道", "仅右声道"]
        print(f"声道模式: {modes[channel_mode]}")
```

#### 3. 运行演示程序

```bash
python audio_integration_demo.py
```

## 🔧 核心功能详解

### 音频设备检测

系统使用Qt的`QAudioDeviceInfo`类来检测音频设备：

```python
def load_audio_devices(self):
    """加载音频输出设备"""
    devices = QAudioDeviceInfo.availableDevices(QAudio.AudioOutput)
    
    for device in devices:
        if not device.isNull():
            device_name = device.deviceName()
            # 添加到设备列表
```

### 声道处理

支持多种声道处理模式：

```python
def apply_channel_processing(self, audio_data):
    """应用声道处理"""
    if mode_id == 1:  # 仅左声道
        audio_data[:, 1] = 0  # 右声道静音
    elif mode_id == 2:  # 仅右声道
        audio_data[:, 0] = 0  # 左声道静音
    else:  # 立体声模式，应用平衡
        # 平衡调节逻辑
```

### 音频播放

通过指定设备播放音频：

```python
def play_audio_through_device(self, audio_data, sample_rate):
    """通过指定设备播放音频"""
    # 设置音频格式
    format = QAudioFormat()
    format.setSampleRate(sample_rate)
    format.setChannelCount(2)
    format.setSampleSize(16)
    
    # 创建音频输出
    audio_output = QAudioOutput(selected_device, format)
    
    # 开始播放
    io_device = audio_output.start()
    io_device.write(audio_bytes)
```

## 🎛️ 界面功能说明

### 主要区域

1. **设备选择区域**
   - 设备下拉列表：显示所有可用的音频输出设备
   - 刷新按钮：重新检测音频设备
   - 设备信息：显示选中设备的详细信息

2. **声道配置区域**
   - 声道模式选择：立体声/仅左声道/仅右声道
   - 平衡滑块：调节左右声道平衡
   - 实时预览：当前声道配置的可视化显示

3. **音频测试区域**
   - 声道测试按钮：分别测试左右声道和立体声
   - 音量控制：调节测试音频的音量
   - 测试音调生成：440Hz标准测试音调

4. **状态显示区域**
   - 操作日志：显示所有操作的状态信息
   - 错误提示：显示错误信息和解决建议
   - 实时状态：当前设备和配置的状态

### 快捷操作

- **Ctrl+R**: 刷新设备列表
- **Space**: 播放/暂停测试音频
- **Esc**: 关闭设备管理器
- **Enter**: 应用当前配置

## 🔌 集成指南

### 集成到现有项目

1. **复制核心文件**
   ```bash
   cp modern_audio_device_panel.py your_project/
   cp modern_ui_components.py your_project/
   cp modern_ui_styles.py your_project/
   ```

2. **修改现有代码**
   ```python
   # 在你的主窗口类中
   from modern_audio_device_panel import ModernAudioDevicePanel
   
   class MainWindow(QMainWindow):
       def __init__(self):
           super().__init__()
           self.setup_audio_device_panel()
           
       def setup_audio_device_panel(self):
           """设置音频设备面板"""
           self.audio_panel = ModernAudioDevicePanel()
           
           # 连接信号
           self.audio_panel.device_changed.connect(self.on_audio_device_changed)
           self.audio_panel.channel_changed.connect(self.on_audio_channel_changed)
           
           # 添加到布局
           self.layout.addWidget(self.audio_panel)
   ```

3. **处理设备配置**
   ```python
   def on_audio_device_changed(self, device):
       """处理设备改变"""
       self.current_audio_device = device
       # 更新你的音频播放逻辑
       
   def on_audio_channel_changed(self, channel_mode):
       """处理声道改变"""
       self.current_channel_mode = channel_mode
       # 更新声道处理逻辑
   ```

### 自定义样式

你可以通过修改`modern_ui_styles.py`来自定义界面样式：

```python
# 修改颜色方案
COLORS = {
    'primary': '#your_primary_color',      # 主色调
    'secondary': '#your_secondary_color',  # 辅助色
    'background': '#your_background_color', # 背景色
    # ... 其他颜色
}
```

## 🧪 测试功能

### 音频设备测试

系统提供了完整的音频设备测试功能：

1. **设备检测测试**: 验证系统能否正确检测所有音频设备
2. **声道输出测试**: 测试左右声道是否正常工作
3. **音质测试**: 播放标准测试音调验证音质
4. **延迟测试**: 测量音频输出延迟

### 运行测试

```bash
# 运行完整的演示程序
python audio_integration_demo.py

# 运行设备管理器
python audio_device_manager.py

# 运行简单测试
python -c "
from modern_audio_device_panel import ModernAudioDevicePanel
from PyQt5.QtWidgets import QApplication
import sys

app = QApplication(sys.argv)
panel = ModernAudioDevicePanel()
panel.show()
app.exec_()
"
```

## 🐛 故障排除

### 常见问题

1. **设备检测不到**
   - 检查音频设备驱动是否正确安装
   - 确认设备在系统音频设置中可见
   - 尝试重新插拔USB音频设备

2. **声音无法输出**
   - 检查设备是否被其他应用程序占用
   - 确认音量设置不为0
   - 验证音频格式是否被设备支持

3. **界面显示异常**
   - 确认PyQt5版本兼容性
   - 检查系统字体设置
   - 尝试重新启动应用程序

4. **声道测试无效果**
   - 确认使用的是立体声音频设备
   - 检查音频线缆连接
   - 验证设备驱动程序

### 调试模式

启用调试模式获取详细日志：

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# 然后运行你的应用程序
```

## 📈 性能优化

### 内存管理

- 音频缓冲区自动管理，防止内存泄漏
- 设备对象的正确释放
- 临时文件的及时清理

### 响应性优化

- 异步设备检测，避免界面卡顿
- 音频处理在后台线程执行
- UI更新使用信号槽机制

## 🔄 更新日志

### v2.0.0 (当前版本)
- ✅ 全新的现代化UI设计
- ✅ 完整的声道控制功能
- ✅ 设备热插拔支持
- ✅ 音频测试功能
- ✅ 集成演示程序

### v1.0.0
- ✅ 基础音频设备检测
- ✅ 简单的设备选择功能

## 🤝 贡献指南

欢迎提交问题报告和功能请求！

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如果你在使用过程中遇到问题，可以通过以下方式获取帮助：

- 📧 邮件支持: <EMAIL>
- 💬 在线讨论: [GitHub Issues](https://github.com/your-repo/issues)
- 📚 文档中心: [Wiki](https://github.com/your-repo/wiki)

---

**🎵 享受现代化的音频设备管理体验！**
