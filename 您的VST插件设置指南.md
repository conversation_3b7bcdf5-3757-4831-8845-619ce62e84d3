# 🎛️ 您的VST插件设置指南

## 📋 您的VST插件列表

根据您提供的截图，您有以下3个VST插件：

1. **Auburn Sounds Graillon 3-64** 🎵 - 专业变声/音调处理
2. **TAL-Reverb-4-64** 🌊 - 高质量混响效果
3. **TSE_808_2.0_x64** 🔥 - 经典808失真/过载

## 🎯 推荐的使用方案

### 方案1：音调 + 失真组合（推荐）

#### Auburn Sounds Graillon 3-64 → 音调控制
```
用途：实现变声和音调变化效果
OBS滤镜名称：Graillon音调
参数控制：Pitch/Semitones（音调/半音）
```

#### TSE_808_2.0_x64 → 失真控制  
```
用途：增加温暖的失真和过载效果
OBS滤镜名称：TSE808失真
参数控制：Drive（驱动）+ Tone（音色）
```

### 方案2：完整音频链（高级）
```
音频源 → Graillon音调 → TSE808失真 → TAL混响 → 输出
```

## 🔧 OBS中的具体设置步骤

### 步骤1：添加Auburn Sounds Graillon 3-64

1. **添加滤镜**：
   - 右键音频源 → 滤镜 → 添加 → VST 2.x 插件
   - 滤镜名称：`Graillon音调`
   - 插件路径：选择 `Auburn Sounds Graillon 3-64`

2. **插件参数设置**：
   ```
   Pitch: 0 semitones (默认)
   Formant: 100% (保持自然)
   Mix: 100% (全湿信号)
   ```

3. **程序中对应设置**：
   ```
   滤镜名称：Graillon音调
   音调范围：-6 到 +6 半音
   变化间隔：3 秒
   ```

### 步骤2：添加TSE_808_2.0_x64

1. **添加滤镜**：
   - 滤镜名称：`TSE808失真`
   - 插件路径：选择 `TSE_808_2.0_x64`

2. **插件参数设置**：
   ```
   Drive: 30% (中等失真)
   Tone: 50% (平衡音色)
   Level: 80% (输出电平)
   ```

3. **程序中对应设置**：
   ```
   滤镜名称：TSE808失真
   失真强度范围：10-70%
   音色范围：20-80%
   变化间隔：3 秒
   ```

### 步骤3：添加TAL-Reverb-4-64（可选）

1. **添加滤镜**：
   - 滤镜名称：`TAL混响`
   - 插件路径：选择 `TAL-Reverb-4-64`

2. **插件参数设置**：
   ```
   Room Size: 40% (中等房间)
   Damping: 60% (适度阻尼)
   Mix: 25% (轻微混响)
   ```

## 🎛️ 程序中的设置

### 更新后的默认设置

程序已经更新为适配您的插件：

#### VST音调控制（Graillon）
```
默认滤镜名称：Graillon音调
默认音调范围：-6 到 +6 半音
默认变化间隔：3 秒
```

#### VST失真控制（TSE808）
```
默认滤镜名称：TSE808失真
默认失真强度：10-70%
默认音色范围：20-80%
默认变化间隔：3 秒
```

## 🧪 测试步骤

### 1. 基础测试

1. **设置音频源**：
   - 在程序中选择您的音频源（如麦克风）
   - 确保OBS中该源已添加对应的VST滤镜

2. **测试Graillon音调**：
   ```
   ✅ 启用VST音调控制
   ✅ 滤镜名称：Graillon音调
   ✅ 音调范围：-3 到 +3 半音（保守测试）
   ✅ 间隔：5秒（便于观察）
   ```

3. **测试TSE808失真**：
   ```
   ✅ 启用VST失真控制
   ✅ 滤镜名称：TSE808失真
   ✅ 失真强度：20-50%
   ✅ 音色：30-70%
   ✅ 间隔：4秒
   ```

### 2. 参数映射验证

#### Graillon 3-64常见参数名
```
- pitch: 音调偏移（半音）
- semitones: 半音数
- formant: 共振峰
- mix: 干湿比
```

#### TSE 808常见参数名
```
- drive: 失真驱动
- tone: 音色控制
- level: 输出电平
- gain: 增益
```

## 🎯 推荐使用场景

### 直播场景
```
游戏直播：
- Graillon: -2到+2半音，5秒间隔
- TSE808: 15-40%失真，轻微变化

聊天直播：
- Graillon: -1到+1半音，8秒间隔
- TSE808: 10-30%失真，温和效果
```

### 录制场景
```
视频制作：
- 更大的参数范围
- 更短的变化间隔
- 配合混响使用

播客录制：
- 保守的参数设置
- 重点在音调变化
- 轻微的失真增色
```

## 🔧 高级技巧

### 1. 滤镜顺序优化
```
推荐顺序：
音频源 → Graillon音调 → TSE808失真 → TAL混响
```

### 2. 参数联动
```python
# 可以在代码中添加参数联动
# 例如：音调越高，失真越小
if pitch > 0:
    drive = base_drive * (1 - pitch/12)
```

### 3. 预设管理
```
为不同场景创建OBS场景集合：
- 游戏场景：强烈效果
- 聊天场景：温和效果
- 音乐场景：专业效果
```

## ⚠️ 注意事项

### 性能优化
- 这3个插件都是高质量插件，会消耗一定CPU
- 建议监控CPU使用率
- 必要时调整缓冲区大小

### 音频质量
- Graillon和TSE808都是专业级插件
- 音质损失很小
- 建议使用48kHz采样率

### 兼容性
- 确保插件版本与您的系统匹配（64位）
- 定期更新插件到最新版本

## 🎉 开始使用

现在您可以：

1. ✅ 在OBS中添加这些VST滤镜
2. ✅ 使用程序中更新的默认设置
3. ✅ 享受专业级的音频效果控制

您的VST插件组合非常专业，特别是Graillon 3-64是业界知名的变声插件，TSE808也是经典的失真效果器。配合我们的自动控制功能，将为您的音频带来丰富的变化！🎵🔥
