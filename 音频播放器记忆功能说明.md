# 🧠 音频播放器参数记忆功能

## 📋 功能概述

现在您的音频播放器已经具备了完整的参数记忆功能！所有的数字设置都会被自动保存，下次启动程序时会自动恢复到上次的设置。

## ✅ 已添加记忆功能的参数

### 🎵 音频效果参数
- **音量范围**：最小值和最大值
- **增益范围**：最小值和最大值（dB）
- **音调范围**：最小值和最大值（倍数）

### ⏰ 播放控制参数
- **间隔时间范围**：最小值和最大值（秒）
- **播放模式**：完整播放 / 片段播放
- **片段播放时长**：秒数

### 🔊 设备设置（已有）
- **音频输出设备**：记住选择的声卡
- **音频文件夹路径**：记住选择的文件夹

## 🔧 工作原理

### 自动保存机制
```python
# 参数变化时自动保存
self.volume1_min_spin.valueChanged.connect(self.save_audio_player_settings)
self.volume1_max_spin.valueChanged.connect(self.save_audio_player_settings)
# ... 其他参数类似
```

### 启动时自动加载
```python
# 程序启动时自动加载保存的参数
def load_audio_player_settings(self):
    settings = QSettings("OBS去重工具", "AudioPlayer")
    
    # 加载音量范围
    volume_min = settings.value("volume_min", 0.2, type=float)
    volume_max = settings.value("volume_max", 0.8, type=float)
    self.volume1_min_spin.setValue(volume_min)
    self.volume1_max_spin.setValue(volume_max)
    # ... 其他参数类似
```

### 关闭时保存
```python
# 程序关闭时确保保存所有设置
def closeEvent(self, event):
    self.save_settings()  # OBS控制参数
    self.save_audio_player_settings()  # 音频播放器参数
    super().closeEvent(event)
```

## 🧪 测试验证

### 使用测试工具
运行测试程序来验证记忆功能：
```bash
python test_audio_memory.py
```

### 测试步骤
1. **修改参数**：调整音量、增益、音调等参数
2. **观察自动保存**：每次修改都会自动保存
3. **关闭程序**：完全退出测试程序
4. **重新启动**：再次运行程序
5. **验证恢复**：检查参数是否恢复到修改后的值

### 预期结果
- ✅ 参数修改后立即自动保存
- ✅ 程序重启后参数完全恢复
- ✅ 控制台显示保存/加载成功信息

## 📊 存储位置

### Windows注册表位置
```
HKEY_CURRENT_USER\Software\OBS去重工具\AudioPlayer\
├── volume_min          # 音量最小值
├── volume_max          # 音量最大值
├── gain_min            # 增益最小值
├── gain_max            # 增益最大值
├── pitch_min           # 音调最小值
├── pitch_max           # 音调最大值
├── interval_min        # 间隔最小值
├── interval_max        # 间隔最大值
├── play_mode           # 播放模式
├── segment_duration    # 片段时长
├── last_audio_device   # 音频设备（已有）
└── last_audio_folder   # 音频文件夹（已有）
```

## 🔧 技术实现

### 关键代码修改

#### 1. 添加加载方法
```python
def load_audio_player_settings(self):
    """加载音频播放器参数设置"""
    settings = QSettings("OBS去重工具", "AudioPlayer")
    
    # 加载各种参数...
    volume_min = settings.value("volume_min", 0.2, type=float)
    self.volume1_min_spin.setValue(volume_min)
    # ...
```

#### 2. 添加保存方法
```python
def save_audio_player_settings(self):
    """保存音频播放器参数设置"""
    settings = QSettings("OBS去重工具", "AudioPlayer")
    
    # 保存各种参数...
    settings.setValue("volume_min", self.volume1_min_spin.value())
    # ...
```

#### 3. 连接变化信号
```python
def connect_audio_player_signals(self):
    """连接音频播放器参数变化信号"""
    self.volume1_min_spin.valueChanged.connect(self.save_audio_player_settings)
    # ... 其他控件类似
```

#### 4. 修改初始化流程
```python
# 在 __init__ 方法中添加
self.load_audio_player_settings()      # 加载保存的参数
self.connect_audio_player_signals()    # 连接自动保存信号
```

#### 5. 修改关闭事件
```python
def closeEvent(self, event):
    self.save_settings()                # 原有的OBS参数保存
    self.save_audio_player_settings()   # 新增的音频参数保存
    super().closeEvent(event)
```

## 🎯 使用体验

### 现在您可以：
1. ✅ **设置一次，永久记住**：调整好参数后不用每次重新设置
2. ✅ **实时自动保存**：每次修改都会立即保存，不怕意外关闭
3. ✅ **智能恢复**：程序启动时自动恢复到上次的设置
4. ✅ **无感知操作**：保存和加载都在后台自动进行

### 典型使用场景
```
第一次使用：
1. 设置音量范围：0.3 - 0.7
2. 设置增益范围：-1.5 - 1.5 dB
3. 设置播放模式：片段播放，3秒
4. 选择音频设备：NVIDIA音频
✅ 所有设置自动保存

下次启动：
✅ 音量范围：0.3 - 0.7（已恢复）
✅ 增益范围：-1.5 - 1.5 dB（已恢复）
✅ 播放模式：片段播放，3秒（已恢复）
✅ 音频设备：NVIDIA音频（已恢复）
```

## 🔍 故障排除

### 如果参数没有被记住：
1. **检查权限**：确保程序有写入注册表的权限
2. **查看日志**：控制台会显示保存/加载的状态信息
3. **手动测试**：使用测试工具验证基本功能
4. **重置设置**：如果出现问题，可以清除注册表重新开始

### 常见问题
- **Q**: 为什么有些参数记住了，有些没有？
- **A**: 检查控制台日志，可能某些控件的信号连接失败

- **Q**: 如何清除所有保存的设置？
- **A**: 删除注册表中的 `HKEY_CURRENT_USER\Software\OBS去重工具` 项

- **Q**: 参数保存在哪里？
- **A**: Windows注册表，使用QSettings自动管理

现在您的音频播放器具备了完整的记忆功能，使用体验将大大提升！🎉
