# -*- coding: utf-8 -*-
"""
音频设备管理器
集成现代化音频设备选择和声道控制功能
"""

import sys
import os
import numpy as np
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTabWidget, QSplitter, QFrame, QLabel, QPushButton, QMessageBox
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QIcon, QFont
from PyQt5.QtMultimedia import QAudioDeviceInfo, QAudio, QAudioOutput, QAudioFormat
from modern_audio_device_panel import ModernAudioDevicePanel

try:
    import soundfile as sf
    AUDIO_PROCESSING_AVAILABLE = True
except ImportError:
    AUDIO_PROCESSING_AVAILABLE = False

class AudioDeviceManager(QMainWindow):
    """音频设备管理器主窗口"""
    
    # 信号定义
    device_configured = pyqtSignal(object, dict)  # 设备配置完成信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_device = None
        self.current_config = {}
        self.audio_output = None
        self.setup_ui()
        self.setup_connections()
        
    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("🎵 音频设备管理器 - 现代化版本")
        self.setGeometry(200, 100, 900, 700)
        self.setMinimumSize(800, 600)
        
        # 设置窗口图标
        try:
            self.setWindowIcon(QIcon('audio_icon.ico'))
        except:
            pass
            
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(0)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建标题栏
        self.create_title_bar(main_layout)
        
        # 创建主内容区域
        self.create_main_content(main_layout)
        
        # 创建底部工具栏
        self.create_bottom_toolbar(main_layout)
        
        # 应用现代化样式
        self.apply_modern_style()
        
    def create_title_bar(self, parent_layout):
        """创建标题栏"""
        title_frame = QFrame()
        title_frame.setObjectName("titleFrame")
        title_frame.setFixedHeight(80)
        title_layout = QHBoxLayout(title_frame)
        title_layout.setContentsMargins(30, 15, 30, 15)
        
        # 标题和图标
        title_widget = QWidget()
        title_widget_layout = QVBoxLayout(title_widget)
        title_widget_layout.setContentsMargins(0, 0, 0, 0)
        title_widget_layout.setSpacing(2)
        
        main_title = QLabel("🎵 音频设备管理器")
        main_title.setObjectName("mainTitle")
        
        subtitle = QLabel("专业级音频输出设备配置与声道控制")
        subtitle.setObjectName("subtitle")
        
        title_widget_layout.addWidget(main_title)
        title_widget_layout.addWidget(subtitle)
        
        # 快速操作按钮
        quick_actions = QWidget()
        quick_layout = QHBoxLayout(quick_actions)
        quick_layout.setContentsMargins(0, 0, 0, 0)
        quick_layout.setSpacing(10)
        
        self.refresh_all_btn = QPushButton("🔄 刷新所有")
        self.refresh_all_btn.setObjectName("quickActionButton")
        self.refresh_all_btn.clicked.connect(self.refresh_all_devices)
        
        self.save_config_btn = QPushButton("💾 保存配置")
        self.save_config_btn.setObjectName("quickActionButton")
        self.save_config_btn.clicked.connect(self.save_current_config)
        
        self.load_config_btn = QPushButton("📂 加载配置")
        self.load_config_btn.setObjectName("quickActionButton")
        self.load_config_btn.clicked.connect(self.load_saved_config)
        
        quick_layout.addWidget(self.refresh_all_btn)
        quick_layout.addWidget(self.save_config_btn)
        quick_layout.addWidget(self.load_config_btn)
        quick_layout.addStretch()
        
        title_layout.addWidget(title_widget, 2)
        title_layout.addWidget(quick_actions, 1)
        
        parent_layout.addWidget(title_frame)
        
    def create_main_content(self, parent_layout):
        """创建主内容区域"""
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        splitter.setObjectName("mainSplitter")
        
        # 左侧：音频设备配置面板
        self.device_panel = ModernAudioDevicePanel()
        splitter.addWidget(self.device_panel)
        
        # 右侧：高级功能面板
        self.create_advanced_panel(splitter)
        
        # 设置分割比例
        splitter.setSizes([500, 400])
        parent_layout.addWidget(splitter)
        
    def create_advanced_panel(self, parent):
        """创建高级功能面板"""
        advanced_widget = QWidget()
        advanced_layout = QVBoxLayout(advanced_widget)
        advanced_layout.setContentsMargins(20, 20, 20, 20)
        advanced_layout.setSpacing(20)
        
        # 标题
        advanced_title = QLabel("⚙️ 高级音频功能")
        advanced_title.setObjectName("panelTitle")
        advanced_layout.addWidget(advanced_title)
        
        # 创建标签页
        self.advanced_tabs = QTabWidget()
        self.advanced_tabs.setObjectName("advancedTabs")
        
        # 音频处理标签页
        self.create_audio_processing_tab()
        
        # 设备监控标签页
        self.create_device_monitoring_tab()
        
        # 配置管理标签页
        self.create_config_management_tab()
        
        advanced_layout.addWidget(self.advanced_tabs)
        parent.addWidget(advanced_widget)
        
    def create_audio_processing_tab(self):
        """创建音频处理标签页"""
        processing_widget = QWidget()
        processing_layout = QVBoxLayout(processing_widget)
        processing_layout.setSpacing(15)
        
        # 音频效果控制
        effects_frame = QFrame()
        effects_frame.setObjectName("effectsFrame")
        effects_layout = QVBoxLayout(effects_frame)
        
        effects_title = QLabel("🎛️ 音频效果")
        effects_title.setObjectName("sectionTitle")
        effects_layout.addWidget(effects_title)
        
        # 这里可以添加各种音频效果控件
        # 例如：均衡器、混响、压缩器等
        
        processing_layout.addWidget(effects_frame)
        processing_layout.addStretch()
        
        self.advanced_tabs.addTab(processing_widget, "🎛️ 音频处理")
        
    def create_device_monitoring_tab(self):
        """创建设备监控标签页"""
        monitoring_widget = QWidget()
        monitoring_layout = QVBoxLayout(monitoring_widget)
        monitoring_layout.setSpacing(15)
        
        # 设备状态监控
        status_frame = QFrame()
        status_frame.setObjectName("statusFrame")
        status_layout = QVBoxLayout(status_frame)
        
        status_title = QLabel("📊 设备状态监控")
        status_title.setObjectName("sectionTitle")
        status_layout.addWidget(status_title)
        
        # 这里可以添加设备状态监控控件
        # 例如：音频电平表、延迟监控、设备健康状态等
        
        monitoring_layout.addWidget(status_frame)
        monitoring_layout.addStretch()
        
        self.advanced_tabs.addTab(monitoring_widget, "📊 设备监控")
        
    def create_config_management_tab(self):
        """创建配置管理标签页"""
        config_widget = QWidget()
        config_layout = QVBoxLayout(config_widget)
        config_layout.setSpacing(15)
        
        # 配置管理
        config_frame = QFrame()
        config_frame.setObjectName("configFrame")
        config_frame_layout = QVBoxLayout(config_frame)
        
        config_title = QLabel("⚙️ 配置管理")
        config_title.setObjectName("sectionTitle")
        config_frame_layout.addWidget(config_title)
        
        # 这里可以添加配置管理控件
        # 例如：配置文件列表、导入导出、预设管理等
        
        config_layout.addWidget(config_frame)
        config_layout.addStretch()
        
        self.advanced_tabs.addTab(config_widget, "⚙️ 配置管理")
        
    def create_bottom_toolbar(self, parent_layout):
        """创建底部工具栏"""
        toolbar_frame = QFrame()
        toolbar_frame.setObjectName("bottomToolbar")
        toolbar_frame.setFixedHeight(60)
        toolbar_layout = QHBoxLayout(toolbar_frame)
        toolbar_layout.setContentsMargins(30, 10, 30, 10)
        
        # 状态信息
        self.status_label = QLabel("✅ 音频设备管理器已就绪")
        self.status_label.setObjectName("statusLabel")
        
        # 应用按钮
        self.apply_btn = QPushButton("✅ 应用配置")
        self.apply_btn.setObjectName("applyButton")
        self.apply_btn.clicked.connect(self.apply_configuration)
        
        self.cancel_btn = QPushButton("❌ 取消")
        self.cancel_btn.setObjectName("cancelButton")
        self.cancel_btn.clicked.connect(self.close)
        
        toolbar_layout.addWidget(self.status_label)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(self.apply_btn)
        toolbar_layout.addWidget(self.cancel_btn)
        
        parent_layout.addWidget(toolbar_frame)
        
    def setup_connections(self):
        """设置信号连接"""
        # 连接设备面板的信号
        self.device_panel.device_changed.connect(self.on_device_changed)
        self.device_panel.channel_changed.connect(self.on_channel_changed)
        self.device_panel.volume_changed.connect(self.on_volume_changed)
        
    def on_device_changed(self, device):
        """设备改变事件处理"""
        self.current_device = device
        if device:
            device_name = device.deviceName() if device else "系统默认"
            self.status_label.setText(f"🔊 当前设备: {device_name}")
        else:
            self.status_label.setText("🔊 当前设备: 系统默认")
            
    def on_channel_changed(self, channel_mode):
        """声道改变事件处理"""
        mode_names = ["立体声", "仅左声道", "仅右声道"]
        mode_name = mode_names[channel_mode] if 0 <= channel_mode < len(mode_names) else "未知"
        self.status_label.setText(f"🎚️ 声道模式: {mode_name}")
        
    def on_volume_changed(self, volume):
        """音量改变事件处理"""
        self.status_label.setText(f"🔊 音量: {volume}%")
        
    def refresh_all_devices(self):
        """刷新所有设备"""
        self.device_panel.refresh_devices()
        self.status_label.setText("🔄 设备列表已刷新")
        
    def save_current_config(self):
        """保存当前配置"""
        config = self.device_panel.get_current_config()
        # 这里可以实现配置保存逻辑
        QMessageBox.information(self, "保存配置", "配置已保存到本地文件")
        
    def load_saved_config(self):
        """加载保存的配置"""
        # 这里可以实现配置加载逻辑
        QMessageBox.information(self, "加载配置", "配置已从本地文件加载")
        
    def apply_configuration(self):
        """应用当前配置"""
        config = self.device_panel.get_current_config()
        self.current_config = config
        
        # 发送配置完成信号
        self.device_configured.emit(self.current_device, config)
        
        QMessageBox.information(self, "应用配置", "音频设备配置已应用")
        self.close()
        
    def apply_modern_style(self):
        """应用现代化样式"""
        self.setStyleSheet("""
            /* 主窗口样式 */
            QMainWindow {
                background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                color: #1e293b;
                font-family: 'Inter', 'Noto Sans SC', 'Microsoft YaHei', sans-serif;
            }
            
            /* 标题栏样式 */
            #titleFrame {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border-radius: 0px;
            }
            
            #mainTitle {
                font-size: 20pt;
                font-weight: bold;
                color: white;
            }
            
            #subtitle {
                font-size: 12pt;
                color: rgba(255, 255, 255, 0.8);
            }
            
            /* 快速操作按钮 */
            #quickActionButton {
                background: rgba(255, 255, 255, 0.2);
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
            }
            
            #quickActionButton:hover {
                background: rgba(255, 255, 255, 0.3);
                border-color: rgba(255, 255, 255, 0.5);
            }
            
            /* 分割器样式 */
            #mainSplitter::handle {
                background: #e2e8f0;
                width: 2px;
            }
            
            #mainSplitter::handle:hover {
                background: #667eea;
            }
            
            /* 面板标题 */
            #panelTitle {
                font-size: 16pt;
                font-weight: bold;
                color: #667eea;
                padding: 10px 0;
                border-bottom: 2px solid #e2e8f0;
                margin-bottom: 15px;
            }
            
            /* 标签页样式 */
            #advancedTabs::pane {
                border: 2px solid #e2e8f0;
                border-radius: 8px;
                background: white;
            }
            
            #advancedTabs::tab-bar {
                alignment: center;
            }
            
            #advancedTabs QTabBar::tab {
                background: #f1f5f9;
                color: #64748b;
                border: 1px solid #e2e8f0;
                padding: 10px 20px;
                margin-right: 2px;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
            }
            
            #advancedTabs QTabBar::tab:selected {
                background: white;
                color: #667eea;
                border-bottom-color: white;
            }
            
            #advancedTabs QTabBar::tab:hover {
                background: #e2e8f0;
            }
            
            /* 底部工具栏 */
            #bottomToolbar {
                background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                border-top: 1px solid #e2e8f0;
            }
            
            #statusLabel {
                font-size: 11pt;
                color: #64748b;
                font-weight: 500;
            }
            
            #applyButton {
                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 25px;
                font-weight: bold;
                font-size: 11pt;
            }
            
            #applyButton:hover {
                background: linear-gradient(135deg, #0d9488 0%, #047857 100%);
            }
            
            #cancelButton {
                background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 25px;
                font-weight: bold;
                font-size: 11pt;
            }
            
            #cancelButton:hover {
                background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            }
            
            /* 框架样式 */
            QFrame {
                background: white;
                border: 1px solid #e2e8f0;
                border-radius: 8px;
                padding: 15px;
            }
            
            #sectionTitle {
                font-size: 14pt;
                font-weight: bold;
                color: #1e293b;
                padding: 5px 0;
                border-bottom: 1px solid #e2e8f0;
                margin-bottom: 10px;
            }
        """)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("音频设备管理器")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("Modern Audio Solutions")
    
    # 创建主窗口
    window = AudioDeviceManager()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
