# -*- coding: utf-8 -*-
"""
现代化音频设备配置面板
实现音频输出设备选择和声道配置的现代化UI界面
"""

import sys
import platform
import subprocess
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QComboBox,
    QGroupBox, QFormLayout, QSlider, QSpinBox, QCheckBox, QFrame,
    QScrollArea, QButtonGroup, QRadioButton, QProgressBar, QTextEdit
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QPropertyAnimation, QEasingCurve
from PyQt5.QtGui import QFont, QPainter, QColor, QLinearGradient, QPalette
from PyQt5.QtMultimedia import QAudioDeviceInfo, QAudio, QAudioOutput, QAudioFormat
import numpy as np

class ModernAudioDevicePanel(QWidget):
    """现代化音频设备配置面板"""
    
    # 信号定义
    device_changed = pyqtSignal(object)  # 设备改变信号
    channel_changed = pyqtSignal(int)    # 声道改变信号
    volume_changed = pyqtSignal(int)     # 音量改变信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.selected_device = None
        self.selected_channels = []
        self.current_audio_output = None
        self.setup_ui()
        self.setup_styles()
        self.load_audio_devices()
        
    def setup_ui(self):
        """设置UI界面"""
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题区域
        self.create_header(main_layout)
        
        # 设备选择区域
        self.create_device_selection(main_layout)
        
        # 声道配置区域
        self.create_channel_configuration(main_layout)
        
        # 音频测试区域
        self.create_audio_test(main_layout)
        
        # 状态显示区域
        self.create_status_display(main_layout)
        
    def create_header(self, parent_layout):
        """创建标题区域"""
        header_frame = QFrame()
        header_frame.setObjectName("headerFrame")
        header_layout = QVBoxLayout(header_frame)
        
        # 主标题
        title_label = QLabel("🎵 音频设备配置中心")
        title_label.setObjectName("titleLabel")
        title_label.setAlignment(Qt.AlignCenter)
        
        # 副标题
        subtitle_label = QLabel("选择音频输出设备并配置声道输出")
        subtitle_label.setObjectName("subtitleLabel")
        subtitle_label.setAlignment(Qt.AlignCenter)
        
        header_layout.addWidget(title_label)
        header_layout.addWidget(subtitle_label)
        parent_layout.addWidget(header_frame)
        
    def create_device_selection(self, parent_layout):
        """创建设备选择区域"""
        device_group = QGroupBox("🔊 音频输出设备")
        device_group.setObjectName("deviceGroup")
        device_layout = QFormLayout(device_group)
        device_layout.setSpacing(15)
        
        # 设备选择下拉框
        device_widget = QWidget()
        device_widget_layout = QHBoxLayout(device_widget)
        device_widget_layout.setContentsMargins(0, 0, 0, 0)
        
        self.device_combo = QComboBox()
        self.device_combo.setObjectName("deviceCombo")
        self.device_combo.setMinimumHeight(40)
        self.device_combo.currentTextChanged.connect(self.on_device_changed)
        
        refresh_btn = QPushButton("🔄 刷新设备")
        refresh_btn.setObjectName("refreshButton")
        refresh_btn.clicked.connect(self.refresh_devices)
        refresh_btn.setMaximumWidth(120)
        
        device_widget_layout.addWidget(self.device_combo, 3)
        device_widget_layout.addWidget(refresh_btn, 1)
        
        # 设备信息显示
        self.device_info_label = QLabel("请选择音频输出设备")
        self.device_info_label.setObjectName("deviceInfoLabel")
        self.device_info_label.setWordWrap(True)
        
        device_layout.addRow("选择设备:", device_widget)
        device_layout.addRow("设备信息:", self.device_info_label)
        
        parent_layout.addWidget(device_group)
        
    def create_channel_configuration(self, parent_layout):
        """创建声道配置区域"""
        channel_group = QGroupBox("🎚️ 声道配置")
        channel_group.setObjectName("channelGroup")
        channel_layout = QVBoxLayout(channel_group)
        channel_layout.setSpacing(15)
        
        # 声道选择模式
        mode_widget = QWidget()
        mode_layout = QHBoxLayout(mode_widget)
        mode_layout.setContentsMargins(0, 0, 0, 0)
        
        self.channel_mode_group = QButtonGroup()
        
        self.stereo_radio = QRadioButton("立体声 (L+R)")
        self.stereo_radio.setObjectName("stereoRadio")
        self.stereo_radio.setChecked(True)
        self.stereo_radio.toggled.connect(self.on_channel_mode_changed)
        
        self.left_only_radio = QRadioButton("仅左声道 (L)")
        self.left_only_radio.setObjectName("leftOnlyRadio")
        self.left_only_radio.toggled.connect(self.on_channel_mode_changed)
        
        self.right_only_radio = QRadioButton("仅右声道 (R)")
        self.right_only_radio.setObjectName("rightOnlyRadio")
        self.right_only_radio.toggled.connect(self.on_channel_mode_changed)
        
        self.channel_mode_group.addButton(self.stereo_radio, 0)
        self.channel_mode_group.addButton(self.left_only_radio, 1)
        self.channel_mode_group.addButton(self.right_only_radio, 2)
        
        mode_layout.addWidget(self.stereo_radio)
        mode_layout.addWidget(self.left_only_radio)
        mode_layout.addWidget(self.right_only_radio)
        mode_layout.addStretch()
        
        # 声道平衡控制
        balance_widget = QWidget()
        balance_layout = QFormLayout(balance_widget)
        
        self.balance_slider = QSlider(Qt.Horizontal)
        self.balance_slider.setObjectName("balanceSlider")
        self.balance_slider.setRange(-100, 100)
        self.balance_slider.setValue(0)
        self.balance_slider.setTickPosition(QSlider.TicksBelow)
        self.balance_slider.setTickInterval(25)
        self.balance_slider.valueChanged.connect(self.on_balance_changed)
        
        self.balance_label = QLabel("平衡: 中央")
        self.balance_label.setObjectName("balanceLabel")
        
        balance_layout.addRow("声道平衡:", self.balance_slider)
        balance_layout.addRow("", self.balance_label)
        
        channel_layout.addWidget(QLabel("声道输出模式:"))
        channel_layout.addWidget(mode_widget)
        channel_layout.addWidget(balance_widget)
        
        parent_layout.addWidget(channel_group)
        
    def create_audio_test(self, parent_layout):
        """创建音频测试区域"""
        test_group = QGroupBox("🧪 音频测试")
        test_group.setObjectName("testGroup")
        test_layout = QVBoxLayout(test_group)
        test_layout.setSpacing(15)
        
        # 测试按钮区域
        test_buttons_widget = QWidget()
        test_buttons_layout = QHBoxLayout(test_buttons_widget)
        test_buttons_layout.setSpacing(10)
        
        self.test_left_btn = QPushButton("🔊 测试左声道")
        self.test_left_btn.setObjectName("testLeftButton")
        self.test_left_btn.clicked.connect(lambda: self.test_channel('left'))
        
        self.test_right_btn = QPushButton("🔊 测试右声道")
        self.test_right_btn.setObjectName("testRightButton")
        self.test_right_btn.clicked.connect(lambda: self.test_channel('right'))
        
        self.test_stereo_btn = QPushButton("🎵 测试立体声")
        self.test_stereo_btn.setObjectName("testStereoButton")
        self.test_stereo_btn.clicked.connect(lambda: self.test_channel('stereo'))
        
        test_buttons_layout.addWidget(self.test_left_btn)
        test_buttons_layout.addWidget(self.test_right_btn)
        test_buttons_layout.addWidget(self.test_stereo_btn)
        
        # 音量控制
        volume_widget = QWidget()
        volume_layout = QFormLayout(volume_widget)
        
        self.volume_slider = QSlider(Qt.Horizontal)
        self.volume_slider.setObjectName("volumeSlider")
        self.volume_slider.setRange(0, 100)
        self.volume_slider.setValue(50)
        self.volume_slider.valueChanged.connect(self.on_volume_changed)
        
        self.volume_label = QLabel("50%")
        self.volume_label.setObjectName("volumeLabel")
        self.volume_label.setMinimumWidth(40)
        
        volume_control_widget = QWidget()
        volume_control_layout = QHBoxLayout(volume_control_widget)
        volume_control_layout.setContentsMargins(0, 0, 0, 0)
        volume_control_layout.addWidget(self.volume_slider, 4)
        volume_control_layout.addWidget(self.volume_label, 1)
        
        volume_layout.addRow("测试音量:", volume_control_widget)
        
        test_layout.addWidget(test_buttons_widget)
        test_layout.addWidget(volume_widget)
        
        parent_layout.addWidget(test_group)
        
    def create_status_display(self, parent_layout):
        """创建状态显示区域"""
        status_group = QGroupBox("📊 状态信息")
        status_group.setObjectName("statusGroup")
        status_layout = QVBoxLayout(status_group)
        
        self.status_text = QTextEdit()
        self.status_text.setObjectName("statusText")
        self.status_text.setMaximumHeight(120)
        self.status_text.setReadOnly(True)
        self.status_text.append("✅ 音频设备配置面板已初始化")
        
        status_layout.addWidget(self.status_text)
        parent_layout.addWidget(status_group)
        
    def setup_styles(self):
        """设置现代化样式"""
        self.setStyleSheet("""
            /* 主面板样式 */
            QWidget {
                background: #f8fafc;
                color: #1e293b;
                font-family: 'Inter', 'Noto Sans SC', 'Microsoft YaHei', sans-serif;
                font-size: 11pt;
            }
            
            /* 标题样式 */
            #titleLabel {
                font-size: 18pt;
                font-weight: bold;
                color: #667eea;
                padding: 10px;
                background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
                border-radius: 12px;
                border: 2px solid #e2e8f0;
            }
            
            #subtitleLabel {
                font-size: 12pt;
                color: #64748b;
                padding: 5px;
                margin-bottom: 10px;
            }
            
            /* 分组框样式 */
            QGroupBox {
                font-weight: bold;
                font-size: 12pt;
                color: #1e293b;
                border: 2px solid #e2e8f0;
                border-radius: 12px;
                margin-top: 10px;
                padding-top: 15px;
                background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 5px 10px;
                background: #667eea;
                color: white;
                border-radius: 6px;
            }
            
            /* 下拉框样式 */
            #deviceCombo {
                border: 2px solid #e2e8f0;
                border-radius: 8px;
                padding: 8px 12px;
                background: white;
                font-size: 11pt;
                min-height: 20px;
            }
            
            #deviceCombo:hover {
                border-color: #667eea;
                box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            }
            
            #deviceCombo:focus {
                border-color: #667eea;
                box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
            }
            
            /* 按钮样式 */
            QPushButton {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 11pt;
                min-height: 20px;
            }
            
            QPushButton:hover {
                background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
            }
            
            QPushButton:pressed {
                background: linear-gradient(135deg, #4c51bf 0%, #553c9a 100%);
                transform: translateY(0px);
            }
            
            #refreshButton {
                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            }
            
            #refreshButton:hover {
                background: linear-gradient(135deg, #0d9488 0%, #047857 100%);
            }
            
            /* 单选按钮样式 */
            QRadioButton {
                font-size: 11pt;
                color: #1e293b;
                spacing: 8px;
            }
            
            QRadioButton::indicator {
                width: 18px;
                height: 18px;
                border-radius: 9px;
                border: 2px solid #e2e8f0;
                background: white;
            }
            
            QRadioButton::indicator:checked {
                background: #667eea;
                border-color: #667eea;
            }
            
            QRadioButton::indicator:checked:after {
                content: "";
                width: 8px;
                height: 8px;
                border-radius: 4px;
                background: white;
                margin: 3px;
            }
            
            /* 滑块样式 */
            QSlider::groove:horizontal {
                border: 1px solid #e2e8f0;
                height: 6px;
                background: #f1f5f9;
                border-radius: 3px;
            }
            
            QSlider::handle:horizontal {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border: 2px solid white;
                width: 20px;
                height: 20px;
                margin: -8px 0;
                border-radius: 10px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
            
            QSlider::handle:horizontal:hover {
                background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
                box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
            }
            
            QSlider::sub-page:horizontal {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border-radius: 3px;
            }
            
            /* 文本编辑框样式 */
            #statusText {
                border: 2px solid #e2e8f0;
                border-radius: 8px;
                background: #f8fafc;
                padding: 10px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 10pt;
            }
            
            /* 标签样式 */
            QLabel {
                color: #1e293b;
                font-size: 11pt;
            }
            
            #deviceInfoLabel {
                background: #f1f5f9;
                border: 1px solid #e2e8f0;
                border-radius: 6px;
                padding: 8px;
                color: #64748b;
            }
            
            #balanceLabel, #volumeLabel {
                font-weight: bold;
                color: #667eea;
            }
        """)

    def load_audio_devices(self):
        """加载音频输出设备"""
        try:
            self.device_combo.clear()
            self.device_combo.addItem("🔊 系统默认设备", None)

            # 获取所有可用的音频输出设备
            devices = QAudioDeviceInfo.availableDevices(QAudio.AudioOutput)

            for device in devices:
                if not device.isNull():
                    device_name = device.deviceName()
                    display_name = f"🎵 {device_name}"
                    self.device_combo.addItem(display_name, device)

            self.log_status(f"✅ 检测到 {len(devices)} 个音频输出设备")

        except Exception as e:
            self.log_status(f"❌ 加载音频设备失败: {e}")

    def refresh_devices(self):
        """刷新音频设备列表"""
        self.log_status("🔄 正在刷新音频设备列表...")
        self.load_audio_devices()

    def on_device_changed(self, device_name):
        """设备选择改变事件"""
        try:
            selected_device = self.device_combo.currentData()

            if selected_device:
                self.selected_device = selected_device
                self.update_device_info(selected_device)
                self.log_status(f"✅ 已选择设备: {device_name}")
                self.device_changed.emit(selected_device)
            else:
                self.selected_device = None
                self.device_info_label.setText("使用系统默认音频输出设备")
                self.log_status("ℹ️ 已选择系统默认设备")
                self.device_changed.emit(None)

        except Exception as e:
            self.log_status(f"❌ 设备选择失败: {e}")

    def update_device_info(self, device):
        """更新设备信息显示"""
        try:
            if device and not device.isNull():
                # 获取设备支持的格式
                preferred_format = device.preferredFormat()
                supported_formats = device.supportedSampleRates()

                info_text = f"""
                设备名称: {device.deviceName()}
                采样率: {preferred_format.sampleRate()} Hz
                声道数: {preferred_format.channelCount()}
                采样位数: {preferred_format.sampleSize()} bits
                支持的采样率: {len(supported_formats)} 种
                """

                self.device_info_label.setText(info_text.strip())
            else:
                self.device_info_label.setText("设备信息不可用")

        except Exception as e:
            self.device_info_label.setText(f"获取设备信息失败: {e}")

    def on_channel_mode_changed(self):
        """声道模式改变事件"""
        try:
            if self.stereo_radio.isChecked():
                mode = "stereo"
                self.balance_slider.setEnabled(True)
            elif self.left_only_radio.isChecked():
                mode = "left_only"
                self.balance_slider.setEnabled(False)
                self.balance_slider.setValue(-100)
            elif self.right_only_radio.isChecked():
                mode = "right_only"
                self.balance_slider.setEnabled(False)
                self.balance_slider.setValue(100)

            self.log_status(f"🎚️ 声道模式已切换为: {mode}")
            self.channel_changed.emit(self.channel_mode_group.checkedId())

        except Exception as e:
            self.log_status(f"❌ 声道模式切换失败: {e}")

    def on_balance_changed(self, value):
        """声道平衡改变事件"""
        try:
            if value < -50:
                balance_text = f"左偏 {abs(value)}%"
            elif value > 50:
                balance_text = f"右偏 {value}%"
            else:
                balance_text = "中央"

            self.balance_label.setText(f"平衡: {balance_text}")

        except Exception as e:
            self.log_status(f"❌ 平衡调节失败: {e}")

    def on_volume_changed(self, value):
        """音量改变事件"""
        self.volume_label.setText(f"{value}%")
        self.volume_changed.emit(value)

    def test_channel(self, channel_type):
        """测试指定声道"""
        try:
            if not self.selected_device:
                self.log_status("⚠️ 请先选择音频输出设备")
                return

            self.log_status(f"🧪 开始测试 {channel_type} 声道...")

            # 生成测试音频
            duration = 2.0  # 2秒
            sample_rate = 44100
            frequency = 440.0  # A4音调

            t = np.linspace(0, duration, int(sample_rate * duration), False)
            audio_data = 0.3 * np.sin(2 * np.pi * frequency * t)

            # 根据声道类型处理音频
            if channel_type == "left":
                # 仅左声道
                stereo_audio = np.column_stack((audio_data, np.zeros_like(audio_data)))
            elif channel_type == "right":
                # 仅右声道
                stereo_audio = np.column_stack((np.zeros_like(audio_data), audio_data))
            else:  # stereo
                # 立体声
                stereo_audio = np.column_stack((audio_data, audio_data))

            # 播放测试音频
            success = self.play_audio_through_device(stereo_audio, sample_rate)

            if success:
                self.log_status(f"✅ {channel_type} 声道测试播放成功")
            else:
                self.log_status(f"❌ {channel_type} 声道测试播放失败")

        except Exception as e:
            self.log_status(f"❌ 声道测试失败: {e}")

    def play_audio_through_device(self, audio_data, sample_rate):
        """通过指定设备播放音频"""
        try:
            if not self.selected_device:
                return False

            # 停止之前的播放
            if self.current_audio_output:
                self.current_audio_output.stop()

            # 设置音频格式
            format = QAudioFormat()
            format.setSampleRate(sample_rate)
            format.setChannelCount(2)  # 立体声
            format.setSampleSize(16)
            format.setCodec("audio/pcm")
            format.setByteOrder(QAudioFormat.LittleEndian)
            format.setSampleType(QAudioFormat.SignedInt)

            # 检查设备是否支持该格式
            if not self.selected_device.isFormatSupported(format):
                format = self.selected_device.nearestFormat(format)

            # 转换音频数据为16位整数
            audio_data_int16 = (audio_data * 32767).astype(np.int16)

            # 创建音频输出
            self.current_audio_output = QAudioOutput(self.selected_device, format)

            # 设置音量
            volume_linear = self.volume_slider.value() / 100.0
            self.current_audio_output.setVolume(volume_linear)

            # 创建音频数据的字节流
            audio_bytes = audio_data_int16.tobytes()

            # 开始播放
            io_device = self.current_audio_output.start()
            if io_device:
                bytes_written = io_device.write(audio_bytes)
                return bytes_written > 0
            else:
                return False

        except Exception as e:
            self.log_status(f"❌ 音频播放失败: {e}")
            return False

    def log_status(self, message):
        """记录状态信息"""
        self.status_text.append(message)
        # 自动滚动到底部
        cursor = self.status_text.textCursor()
        cursor.movePosition(cursor.End)
        self.status_text.setTextCursor(cursor)

    def get_current_config(self):
        """获取当前音频配置"""
        return {
            'device': self.selected_device,
            'channel_mode': self.channel_mode_group.checkedId(),
            'balance': self.balance_slider.value(),
            'volume': self.volume_slider.value()
        }

    def apply_channel_processing(self, audio_data):
        """应用声道处理"""
        try:
            if len(audio_data.shape) == 1:
                # 单声道转立体声
                audio_data = np.column_stack((audio_data, audio_data))

            # 获取当前声道模式
            mode_id = self.channel_mode_group.checkedId()
            balance = self.balance_slider.value()

            if mode_id == 1:  # 仅左声道
                audio_data[:, 1] = 0  # 右声道静音
            elif mode_id == 2:  # 仅右声道
                audio_data[:, 0] = 0  # 左声道静音
            else:  # 立体声模式，应用平衡
                if balance != 0:
                    balance_factor = balance / 100.0
                    if balance_factor < 0:  # 左偏
                        audio_data[:, 1] *= (1 + balance_factor)
                    else:  # 右偏
                        audio_data[:, 0] *= (1 - balance_factor)

            return audio_data

        except Exception as e:
            self.log_status(f"❌ 声道处理失败: {e}")
            return audio_data
