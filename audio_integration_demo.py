#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频设备集成演示
展示如何将现代化音频设备管理器集成到现有应用中
"""

import sys
import os
import numpy as np
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QTextEdit, QGroupBox, QFormLayout, QSlider,
    QComboBox, QFileDialog, QMessageBox, QProgressBar
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt5.QtGui import QIcon, QFont
from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent, QAudioOutput
from PyQt5.QtCore import QUrl

# 导入我们的音频设备管理器
from audio_device_manager import AudioDeviceManager
from modern_audio_device_panel import ModernAudioDevicePanel

try:
    import soundfile as sf
    AUDIO_PROCESSING_AVAILABLE = True
except ImportError:
    AUDIO_PROCESSING_AVAILABLE = False
    print("⚠️ soundfile库未安装，某些功能可能不可用")

class AudioIntegrationDemo(QMainWindow):
    """音频设备集成演示主窗口"""
    
    def __init__(self):
        super().__init__()
        self.current_audio_device = None
        self.current_audio_config = {}
        self.media_player = QMediaPlayer()
        self.audio_output = None
        self.audio_files = []
        self.current_file_index = 0
        
        self.setup_ui()
        self.setup_connections()
        self.load_demo_files()
        
    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("🎵 音频设备集成演示 - 现代化版本")
        self.setGeometry(150, 100, 1000, 700)
        self.setMinimumSize(900, 600)
        
        # 设置窗口图标
        try:
            self.setWindowIcon(QIcon('demo_icon.ico'))
        except:
            pass
            
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # 创建标题
        self.create_header(main_layout)
        
        # 创建主要功能区域
        self.create_main_functions(main_layout)
        
        # 创建音频播放控制区域
        self.create_playback_controls(main_layout)
        
        # 创建状态显示区域
        self.create_status_area(main_layout)
        
        # 应用样式
        self.apply_demo_style()
        
    def create_header(self, parent_layout):
        """创建标题区域"""
        header_widget = QWidget()
        header_widget.setObjectName("headerWidget")
        header_layout = QVBoxLayout(header_widget)
        header_layout.setContentsMargins(20, 15, 20, 15)
        
        # 主标题
        title_label = QLabel("🎵 音频设备集成演示")
        title_label.setObjectName("mainTitle")
        title_label.setAlignment(Qt.AlignCenter)
        
        # 描述
        desc_label = QLabel("演示如何在应用中集成现代化音频设备选择和声道控制功能")
        desc_label.setObjectName("descLabel")
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setWordWrap(True)
        
        header_layout.addWidget(title_label)
        header_layout.addWidget(desc_label)
        
        parent_layout.addWidget(header_widget)
        
    def create_main_functions(self, parent_layout):
        """创建主要功能区域"""
        functions_widget = QWidget()
        functions_layout = QHBoxLayout(functions_widget)
        functions_layout.setSpacing(20)
        
        # 左侧：音频设备配置
        self.create_device_config_section(functions_layout)
        
        # 右侧：音频文件管理
        self.create_file_management_section(functions_layout)
        
        parent_layout.addWidget(functions_widget)
        
    def create_device_config_section(self, parent_layout):
        """创建设备配置区域"""
        device_group = QGroupBox("🔊 音频设备配置")
        device_group.setObjectName("deviceGroup")
        device_layout = QVBoxLayout(device_group)
        device_layout.setSpacing(15)
        
        # 当前设备显示
        current_device_widget = QWidget()
        current_device_layout = QFormLayout(current_device_widget)
        
        self.current_device_label = QLabel("未选择设备")
        self.current_device_label.setObjectName("currentDeviceLabel")
        
        self.current_config_label = QLabel("默认配置")
        self.current_config_label.setObjectName("currentConfigLabel")
        
        current_device_layout.addRow("当前设备:", self.current_device_label)
        current_device_layout.addRow("声道配置:", self.current_config_label)
        
        # 配置按钮
        config_buttons_widget = QWidget()
        config_buttons_layout = QHBoxLayout(config_buttons_widget)
        config_buttons_layout.setSpacing(10)
        
        self.open_device_manager_btn = QPushButton("⚙️ 打开设备管理器")
        self.open_device_manager_btn.setObjectName("primaryButton")
        self.open_device_manager_btn.clicked.connect(self.open_device_manager)
        
        self.quick_config_btn = QPushButton("⚡ 快速配置")
        self.quick_config_btn.setObjectName("secondaryButton")
        self.quick_config_btn.clicked.connect(self.show_quick_config)
        
        config_buttons_layout.addWidget(self.open_device_manager_btn)
        config_buttons_layout.addWidget(self.quick_config_btn)
        
        device_layout.addWidget(current_device_widget)
        device_layout.addWidget(config_buttons_widget)
        device_layout.addStretch()
        
        parent_layout.addWidget(device_group, 1)
        
    def create_file_management_section(self, parent_layout):
        """创建文件管理区域"""
        file_group = QGroupBox("📁 音频文件管理")
        file_group.setObjectName("fileGroup")
        file_layout = QVBoxLayout(file_group)
        file_layout.setSpacing(15)
        
        # 文件列表
        self.file_combo = QComboBox()
        self.file_combo.setObjectName("fileCombo")
        self.file_combo.currentIndexChanged.connect(self.on_file_selected)
        
        # 文件操作按钮
        file_buttons_widget = QWidget()
        file_buttons_layout = QHBoxLayout(file_buttons_widget)
        file_buttons_layout.setSpacing(10)
        
        self.add_file_btn = QPushButton("➕ 添加文件")
        self.add_file_btn.setObjectName("secondaryButton")
        self.add_file_btn.clicked.connect(self.add_audio_file)
        
        self.remove_file_btn = QPushButton("➖ 移除文件")
        self.remove_file_btn.setObjectName("secondaryButton")
        self.remove_file_btn.clicked.connect(self.remove_audio_file)
        
        file_buttons_layout.addWidget(self.add_file_btn)
        file_buttons_layout.addWidget(self.remove_file_btn)
        
        # 文件信息显示
        self.file_info_label = QLabel("请选择音频文件")
        self.file_info_label.setObjectName("fileInfoLabel")
        self.file_info_label.setWordWrap(True)
        
        file_layout.addWidget(QLabel("选择音频文件:"))
        file_layout.addWidget(self.file_combo)
        file_layout.addWidget(file_buttons_widget)
        file_layout.addWidget(QLabel("文件信息:"))
        file_layout.addWidget(self.file_info_label)
        file_layout.addStretch()
        
        parent_layout.addWidget(file_group, 1)
        
    def create_playback_controls(self, parent_layout):
        """创建播放控制区域"""
        playback_group = QGroupBox("🎮 播放控制")
        playback_group.setObjectName("playbackGroup")
        playback_layout = QVBoxLayout(playback_group)
        playback_layout.setSpacing(15)
        
        # 播放按钮
        control_buttons_widget = QWidget()
        control_buttons_layout = QHBoxLayout(control_buttons_widget)
        control_buttons_layout.setSpacing(15)
        
        self.play_btn = QPushButton("▶️ 播放")
        self.play_btn.setObjectName("playButton")
        self.play_btn.clicked.connect(self.play_audio)
        
        self.pause_btn = QPushButton("⏸️ 暂停")
        self.pause_btn.setObjectName("pauseButton")
        self.pause_btn.clicked.connect(self.pause_audio)
        
        self.stop_btn = QPushButton("⏹️ 停止")
        self.stop_btn.setObjectName("stopButton")
        self.stop_btn.clicked.connect(self.stop_audio)
        
        self.test_channels_btn = QPushButton("🧪 测试声道")
        self.test_channels_btn.setObjectName("testButton")
        self.test_channels_btn.clicked.connect(self.test_audio_channels)
        
        control_buttons_layout.addWidget(self.play_btn)
        control_buttons_layout.addWidget(self.pause_btn)
        control_buttons_layout.addWidget(self.stop_btn)
        control_buttons_layout.addWidget(self.test_channels_btn)
        control_buttons_layout.addStretch()
        
        # 音量控制
        volume_widget = QWidget()
        volume_layout = QFormLayout(volume_widget)
        
        self.volume_slider = QSlider(Qt.Horizontal)
        self.volume_slider.setObjectName("volumeSlider")
        self.volume_slider.setRange(0, 100)
        self.volume_slider.setValue(50)
        self.volume_slider.valueChanged.connect(self.on_volume_changed)
        
        self.volume_label = QLabel("50%")
        self.volume_label.setObjectName("volumeLabel")
        
        volume_control_widget = QWidget()
        volume_control_layout = QHBoxLayout(volume_control_widget)
        volume_control_layout.setContentsMargins(0, 0, 0, 0)
        volume_control_layout.addWidget(self.volume_slider, 4)
        volume_control_layout.addWidget(self.volume_label, 1)
        
        volume_layout.addRow("音量:", volume_control_widget)
        
        playback_layout.addWidget(control_buttons_widget)
        playback_layout.addWidget(volume_widget)
        
        parent_layout.addWidget(playback_group)
        
    def create_status_area(self, parent_layout):
        """创建状态显示区域"""
        status_group = QGroupBox("📊 状态信息")
        status_group.setObjectName("statusGroup")
        status_layout = QVBoxLayout(status_group)
        
        self.status_text = QTextEdit()
        self.status_text.setObjectName("statusText")
        self.status_text.setMaximumHeight(150)
        self.status_text.setReadOnly(True)
        
        # 添加初始状态信息
        self.log_status("✅ 音频设备集成演示已启动")
        self.log_status("ℹ️ 请先配置音频输出设备，然后选择音频文件进行播放测试")
        
        status_layout.addWidget(self.status_text)
        parent_layout.addWidget(status_group)
        
    def setup_connections(self):
        """设置信号连接"""
        # 媒体播放器状态变化
        self.media_player.stateChanged.connect(self.on_player_state_changed)
        self.media_player.positionChanged.connect(self.on_position_changed)
        self.media_player.durationChanged.connect(self.on_duration_changed)
        
    def open_device_manager(self):
        """打开设备管理器"""
        try:
            self.device_manager = AudioDeviceManager(self)
            self.device_manager.device_configured.connect(self.on_device_configured)
            self.device_manager.show()
            self.log_status("🔧 已打开音频设备管理器")
            
        except Exception as e:
            self.log_status(f"❌ 打开设备管理器失败: {e}")
            QMessageBox.critical(self, "错误", f"无法打开设备管理器:\n{e}")
            
    def show_quick_config(self):
        """显示快速配置面板"""
        try:
            # 创建一个简化的设备配置对话框
            from PyQt5.QtWidgets import QDialog
            
            dialog = QDialog(self)
            dialog.setWindowTitle("⚡ 快速音频设备配置")
            dialog.setModal(True)
            dialog.resize(600, 400)
            
            layout = QVBoxLayout(dialog)
            
            # 嵌入设备配置面板
            device_panel = ModernAudioDevicePanel()
            layout.addWidget(device_panel)
            
            # 按钮
            button_widget = QWidget()
            button_layout = QHBoxLayout(button_widget)
            
            apply_btn = QPushButton("✅ 应用")
            apply_btn.clicked.connect(lambda: self.apply_quick_config(device_panel, dialog))
            
            cancel_btn = QPushButton("❌ 取消")
            cancel_btn.clicked.connect(dialog.reject)
            
            button_layout.addStretch()
            button_layout.addWidget(apply_btn)
            button_layout.addWidget(cancel_btn)
            
            layout.addWidget(button_widget)
            
            dialog.exec_()
            
        except Exception as e:
            self.log_status(f"❌ 快速配置失败: {e}")
            
    def apply_quick_config(self, device_panel, dialog):
        """应用快速配置"""
        try:
            config = device_panel.get_current_config()
            self.on_device_configured(config['device'], config)
            dialog.accept()
            self.log_status("✅ 快速配置已应用")
            
        except Exception as e:
            self.log_status(f"❌ 应用快速配置失败: {e}")
            
    def on_device_configured(self, device, config):
        """设备配置完成事件处理"""
        try:
            self.current_audio_device = device
            self.current_audio_config = config
            
            # 更新显示
            if device:
                device_name = device.deviceName()
                self.current_device_label.setText(device_name)
            else:
                self.current_device_label.setText("系统默认设备")
                
            # 更新声道配置显示
            channel_modes = ["立体声", "仅左声道", "仅右声道"]
            channel_mode = config.get('channel_mode', 0)
            if 0 <= channel_mode < len(channel_modes):
                mode_text = channel_modes[channel_mode]
                balance = config.get('balance', 0)
                if balance != 0:
                    mode_text += f" (平衡: {balance})"
                self.current_config_label.setText(mode_text)
            else:
                self.current_config_label.setText("默认配置")
                
            self.log_status(f"✅ 音频设备配置已更新: {device_name if device else '系统默认'}")
            
        except Exception as e:
            self.log_status(f"❌ 设备配置更新失败: {e}")
            
    def load_demo_files(self):
        """加载演示音频文件"""
        try:
            # 查找当前目录下的音频文件
            audio_extensions = ['.wav', '.mp3', '.flac', '.ogg', '.m4a']
            current_dir = os.getcwd()
            
            for file in os.listdir(current_dir):
                if any(file.lower().endswith(ext) for ext in audio_extensions):
                    file_path = os.path.join(current_dir, file)
                    self.audio_files.append(file_path)
                    self.file_combo.addItem(f"🎵 {file}", file_path)
                    
            if not self.audio_files:
                self.file_combo.addItem("📁 没有找到音频文件", None)
                self.log_status("⚠️ 当前目录下没有找到音频文件，请手动添加")
            else:
                self.log_status(f"✅ 找到 {len(self.audio_files)} 个音频文件")
                
        except Exception as e:
            self.log_status(f"❌ 加载演示文件失败: {e}")
            
    def add_audio_file(self):
        """添加音频文件"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, "选择音频文件", "",
                "音频文件 (*.wav *.mp3 *.flac *.ogg *.m4a);;所有文件 (*)"
            )
            
            if file_path:
                self.audio_files.append(file_path)
                filename = os.path.basename(file_path)
                self.file_combo.addItem(f"🎵 {filename}", file_path)
                self.file_combo.setCurrentIndex(self.file_combo.count() - 1)
                self.log_status(f"✅ 已添加音频文件: {filename}")
                
        except Exception as e:
            self.log_status(f"❌ 添加音频文件失败: {e}")
            
    def remove_audio_file(self):
        """移除音频文件"""
        try:
            current_index = self.file_combo.currentIndex()
            if current_index >= 0 and current_index < len(self.audio_files):
                filename = os.path.basename(self.audio_files[current_index])
                self.audio_files.pop(current_index)
                self.file_combo.removeItem(current_index)
                self.log_status(f"✅ 已移除音频文件: {filename}")
                
        except Exception as e:
            self.log_status(f"❌ 移除音频文件失败: {e}")
            
    def on_file_selected(self, index):
        """文件选择改变事件"""
        try:
            if index >= 0 and index < len(self.audio_files):
                file_path = self.audio_files[index]
                self.update_file_info(file_path)
                self.current_file_index = index
                
        except Exception as e:
            self.log_status(f"❌ 文件选择失败: {e}")
            
    def update_file_info(self, file_path):
        """更新文件信息显示"""
        try:
            if AUDIO_PROCESSING_AVAILABLE:
                # 使用soundfile获取详细信息
                info = sf.info(file_path)
                info_text = f"""
                文件名: {os.path.basename(file_path)}
                格式: {info.format}
                采样率: {info.samplerate} Hz
                声道数: {info.channels}
                时长: {info.duration:.2f} 秒
                帧数: {info.frames}
                """
            else:
                # 基本信息
                file_size = os.path.getsize(file_path)
                info_text = f"""
                文件名: {os.path.basename(file_path)}
                文件大小: {file_size / 1024 / 1024:.2f} MB
                路径: {file_path}
                """
                
            self.file_info_label.setText(info_text.strip())
            
        except Exception as e:
            self.file_info_label.setText(f"获取文件信息失败: {e}")
            
    def play_audio(self):
        """播放音频"""
        try:
            if not self.audio_files or self.current_file_index >= len(self.audio_files):
                self.log_status("⚠️ 请先选择音频文件")
                return
                
            file_path = self.audio_files[self.current_file_index]
            
            # 如果配置了特定设备，使用自定义播放方法
            if self.current_audio_device and AUDIO_PROCESSING_AVAILABLE:
                self.play_with_device_config(file_path)
            else:
                # 使用默认媒体播放器
                media_content = QMediaContent(QUrl.fromLocalFile(file_path))
                self.media_player.setMedia(media_content)
                self.media_player.setVolume(self.volume_slider.value())
                self.media_player.play()
                
            filename = os.path.basename(file_path)
            self.log_status(f"▶️ 开始播放: {filename}")
            
        except Exception as e:
            self.log_status(f"❌ 播放失败: {e}")
            
    def play_with_device_config(self, file_path):
        """使用设备配置播放音频"""
        try:
            # 读取音频文件
            audio_data, sample_rate = sf.read(file_path)
            
            # 应用声道处理（这里需要实现声道处理逻辑）
            processed_audio = self.apply_channel_processing(audio_data)
            
            # 通过指定设备播放
            self.play_audio_through_device(processed_audio, sample_rate)
            
        except Exception as e:
            self.log_status(f"❌ 设备配置播放失败: {e}")
            
    def apply_channel_processing(self, audio_data):
        """应用声道处理"""
        try:
            if len(audio_data.shape) == 1:
                # 单声道转立体声
                audio_data = np.column_stack((audio_data, audio_data))
                
            # 根据配置处理声道
            channel_mode = self.current_audio_config.get('channel_mode', 0)
            balance = self.current_audio_config.get('balance', 0)
            
            if channel_mode == 1:  # 仅左声道
                audio_data[:, 1] = 0
            elif channel_mode == 2:  # 仅右声道
                audio_data[:, 0] = 0
            elif channel_mode == 0 and balance != 0:  # 立体声平衡
                balance_factor = balance / 100.0
                if balance_factor < 0:  # 左偏
                    audio_data[:, 1] *= (1 + balance_factor)
                else:  # 右偏
                    audio_data[:, 0] *= (1 - balance_factor)
                    
            return audio_data
            
        except Exception as e:
            self.log_status(f"❌ 声道处理失败: {e}")
            return audio_data
            
    def play_audio_through_device(self, audio_data, sample_rate):
        """通过指定设备播放音频"""
        # 这里实现通过指定设备播放的逻辑
        # 类似于之前在main_module.py中的实现
        pass
        
    def pause_audio(self):
        """暂停播放"""
        self.media_player.pause()
        self.log_status("⏸️ 播放已暂停")
        
    def stop_audio(self):
        """停止播放"""
        self.media_player.stop()
        if self.audio_output:
            self.audio_output.stop()
        self.log_status("⏹️ 播放已停止")
        
    def test_audio_channels(self):
        """测试音频声道"""
        try:
            if not self.current_audio_device:
                self.log_status("⚠️ 请先配置音频输出设备")
                return
                
            self.log_status("🧪 开始声道测试...")
            
            # 这里可以实现声道测试逻辑
            # 生成测试音调并分别在左右声道播放
            
            self.log_status("✅ 声道测试完成")
            
        except Exception as e:
            self.log_status(f"❌ 声道测试失败: {e}")
            
    def on_volume_changed(self, value):
        """音量改变事件"""
        self.volume_label.setText(f"{value}%")
        self.media_player.setVolume(value)
        
    def on_player_state_changed(self, state):
        """播放器状态改变事件"""
        if state == QMediaPlayer.PlayingState:
            self.play_btn.setText("⏸️ 暂停")
        else:
            self.play_btn.setText("▶️ 播放")
            
    def on_position_changed(self, position):
        """播放位置改变事件"""
        # 可以在这里更新进度条
        pass
        
    def on_duration_changed(self, duration):
        """播放时长改变事件"""
        # 可以在这里设置进度条范围
        pass
        
    def log_status(self, message):
        """记录状态信息"""
        self.status_text.append(message)
        # 自动滚动到底部
        cursor = self.status_text.textCursor()
        cursor.movePosition(cursor.End)
        self.status_text.setTextCursor(cursor)
        
    def apply_demo_style(self):
        """应用演示样式"""
        self.setStyleSheet("""
            /* 主窗口样式 */
            QMainWindow {
                background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                color: #1e293b;
                font-family: 'Inter', 'Noto Sans SC', 'Microsoft YaHei', sans-serif;
                font-size: 11pt;
            }
            
            /* 标题样式 */
            #headerWidget {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border-radius: 12px;
                margin-bottom: 10px;
            }
            
            #mainTitle {
                font-size: 22pt;
                font-weight: bold;
                color: white;
                padding: 5px;
            }
            
            #descLabel {
                font-size: 12pt;
                color: rgba(255, 255, 255, 0.9);
                padding: 5px;
            }
            
            /* 分组框样式 */
            QGroupBox {
                font-weight: bold;
                font-size: 12pt;
                color: #1e293b;
                border: 2px solid #e2e8f0;
                border-radius: 12px;
                margin-top: 15px;
                padding-top: 20px;
                background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 8px 15px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border-radius: 8px;
                font-weight: bold;
            }
            
            /* 按钮样式 */
            #primaryButton {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                border-radius: 10px;
                padding: 12px 24px;
                font-weight: bold;
                font-size: 11pt;
                min-height: 20px;
            }
            
            #primaryButton:hover {
                background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
                transform: translateY(-2px);
                box-shadow: 0 8px 15px rgba(102, 126, 234, 0.3);
            }
            
            #secondaryButton {
                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                color: white;
                border: none;
                border-radius: 10px;
                padding: 12px 24px;
                font-weight: bold;
                font-size: 11pt;
                min-height: 20px;
            }
            
            #secondaryButton:hover {
                background: linear-gradient(135deg, #0d9488 0%, #047857 100%);
                transform: translateY(-2px);
                box-shadow: 0 8px 15px rgba(16, 185, 129, 0.3);
            }
            
            #playButton {
                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                color: white;
                border: none;
                border-radius: 10px;
                padding: 12px 24px;
                font-weight: bold;
                font-size: 12pt;
                min-width: 100px;
            }
            
            #pauseButton, #stopButton {
                background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
                color: white;
                border: none;
                border-radius: 10px;
                padding: 12px 24px;
                font-weight: bold;
                font-size: 12pt;
                min-width: 100px;
            }
            
            #testButton {
                background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
                color: white;
                border: none;
                border-radius: 10px;
                padding: 12px 24px;
                font-weight: bold;
                font-size: 12pt;
                min-width: 100px;
            }
            
            /* 下拉框样式 */
            #fileCombo {
                border: 2px solid #e2e8f0;
                border-radius: 8px;
                padding: 10px 15px;
                background: white;
                font-size: 11pt;
                min-height: 25px;
            }
            
            #fileCombo:hover {
                border-color: #667eea;
                box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            }
            
            /* 标签样式 */
            #currentDeviceLabel, #currentConfigLabel {
                background: #f1f5f9;
                border: 1px solid #e2e8f0;
                border-radius: 6px;
                padding: 8px 12px;
                color: #1e293b;
                font-weight: 500;
            }
            
            #fileInfoLabel {
                background: #f8fafc;
                border: 1px solid #e2e8f0;
                border-radius: 6px;
                padding: 10px;
                color: #64748b;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 10pt;
            }
            
            #volumeLabel {
                font-weight: bold;
                color: #667eea;
                min-width: 50px;
            }
            
            /* 滑块样式 */
            #volumeSlider::groove:horizontal {
                border: 1px solid #e2e8f0;
                height: 8px;
                background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
                border-radius: 4px;
            }
            
            #volumeSlider::handle:horizontal {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border: 2px solid white;
                width: 22px;
                height: 22px;
                margin: -8px 0;
                border-radius: 11px;
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
            }
            
            #volumeSlider::handle:horizontal:hover {
                background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
                box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
            }
            
            #volumeSlider::sub-page:horizontal {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border-radius: 4px;
            }
            
            /* 文本编辑框样式 */
            #statusText {
                border: 2px solid #e2e8f0;
                border-radius: 10px;
                background: #f8fafc;
                padding: 15px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 10pt;
                color: #1e293b;
            }
        """)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("音频设备集成演示")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("Modern Audio Solutions")
    
    # 创建主窗口
    window = AudioIntegrationDemo()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
